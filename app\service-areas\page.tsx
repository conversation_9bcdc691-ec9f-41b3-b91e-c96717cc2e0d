import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MapPin, Phone, Clock } from "lucide-react"
import ServiceAreasMap from "@/components/service-areas-map"

export default function ServiceAreasPage() {
  return (
    <main className="flex-1">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/placeholder.svg?height=1080&width=1920"
            alt="Service area map"
            fill
            priority
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-background/95 via-background/80 to-background/70" />
        </div>
        
        <div className="relative z-10 container px-4 py-16 md:py-24 lg:py-32 md:px-6">
          <div className="max-w-3xl">
            <Badge variant="outline" className="border-primary/50 text-primary mb-4">COVERAGE AREAS</Badge>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6">
              Where We Provide Tire Assistance
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              TireRescuePro provides fast, reliable roadside tire assistance across major metropolitan areas and surrounding regions.
            </p>
          </div>
        </div>
      </section>
      
      {/* Interactive Map Section */}
      <section className="py-12 md:py-16 container px-4 md:px-6">
        <div className="text-center mb-10 space-y-2">
          <Badge variant="outline" className="border-primary/50 text-primary">INTERACTIVE MAP</Badge>
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight">Check Your Coverage</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Use our interactive map to check if your location is within our service area and see estimated response times.
          </p>
        </div>
        
        <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-lg">
          <ServiceAreasMap />
        </div>
      </section>
      
      {/* Primary Service Areas Section */}
      <section className="py-12 md:py-16 bg-muted/30">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10 space-y-2">
            <Badge variant="outline" className="border-primary/50 text-primary">PRIMARY SERVICE AREAS</Badge>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight">Rapid Response Zones</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              In these areas, we guarantee our fastest response times and full service availability.
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                area: "Downtown",
                responseTime: "15-30 minutes",
                services: ["24/7 Emergency Response", "All Vehicle Types", "Full Service Range"],
                image: "/placeholder.svg?height=200&width=300"
              },
              {
                area: "North Side",
                responseTime: "20-35 minutes",
                services: ["24/7 Emergency Response", "All Vehicle Types", "Full Service Range"],
                image: "/placeholder.svg?height=200&width=300"
              },
              {
                area: "South Side",
                responseTime: "25-40 minutes",
                services: ["24/7 Emergency Response", "All Vehicle Types", "Full Service Range"],
                image: "/placeholder.svg?height=200&width=300"
              },
              {
                area: "East Side",
                responseTime: "20-35 minutes",
                services: ["24/7 Emergency Response", "All Vehicle Types", "Full Service Range"],
                image: "/placeholder.svg?height=200&width=300"
              },
              {
                area: "West Side",
                responseTime: "30-45 minutes",
                services: ["24/7 Emergency Response", "All Vehicle Types", "Full Service Range"],
                image: "/placeholder.svg?height=200&width=300"
              },
              {
                area: "Suburbs",
                responseTime: "35-50 minutes",
                services: ["24/7 Emergency Response", "All Vehicle Types", "Full Service Range"],
                image: "/placeholder.svg?height=200&width=300"
              },
            ].map((area, index) => (
              <Card key={index} className="overflow-hidden border-border/50 bg-background/50 backdrop-blur-sm">
                <div className="aspect-[3/2] relative">
                  <Image
                    src={area.image || "/placeholder.svg"}
                    alt={`${area.area} area`}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-background to-transparent" />
                  <div className="absolute bottom-3 left-3 flex items-center gap-1.5">
                    <MapPin className="h-4 w-4 text-primary" />
                    <span className="font-bold text-lg">{area.area}</span>
                  </div>
                </div>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Clock className="h-4 w-4 text-primary" />
                    <span className="font-medium">Response Time: {area.responseTime}</span>
                  </div>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    {area.services.map((service, i) => (
                      <li key={i} className="flex items-center gap-2">
                        <div className="h-1.5 w-1.5 rounded-full bg-primary" />
                        <span>{service}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      
      {/* Extended Coverage Section */}
      <section className="py-12 md:py-16 container px-4 md:px-6">
        <div className="text-center mb-10 space-y-2">
          <Badge variant="outline" className="border-primary/50 text-primary">EXTENDED COVERAGE</Badge>
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight">Additional Service Areas</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            We also provide service in these extended areas with slightly longer response times.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <h3 className="text-2xl font-bold">Extended Service Regions</h3>
            <p className="text-muted-foreground">
              While our primary service areas guarantee the fastest response times, we also provide service to these extended regions. Response times may be longer, but you can still count on our professional service and quality work.
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-6">
              {[
                { area: "North County", time: "45-60 min" },
                { area: "South County", time: "45-60 min" },
                { area: "East County", time: "50-70 min" },
                { area: "West County", time: "50-70 min" },
                { area: "Outer Suburbs", time: "60-90 min" },
                { area: "Rural Areas", time: "90-120 min" },
              ].map((area, index) => (
                <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                  <MapPin className="h-5 w-5 text-primary" />
                  <div>
                    <p className="font-medium">{area.area}</p>
                    <p className="text-xs text-muted-foreground">Response: {area.time}</p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="pt-4">
              <Button className="gap-2">
                <Phone className="h-4 w-4" />
                Call to Confirm Coverage
              </Button>
            </div>
          </div>
          
          <div className="relative">
            <div className="aspect-square rounded-lg overflow-hidden">
              <Image
                src="/placeholder.svg?height=600&width=600"
                alt="Extended service area map"
                width={600}
                height={600}
                className="object-cover"
              />
            </div>
            <div className="absolute -bottom-6 -right-6 h-32 w-32 rounded-full bg-primary/10 blur-3xl" />
          </div>
        </div>
      </section>
    </main>
  );
}
