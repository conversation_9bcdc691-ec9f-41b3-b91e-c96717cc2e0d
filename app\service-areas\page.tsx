"use client"

import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MapPin, Phone, Clock } from "lucide-react"
import ServiceAreasMap from "@/components/service-areas-map"

export default function ServiceAreasPage() {
  return (
    <main className="flex-1">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/placeholder.svg?height=1080&width=1920"
            alt="Service area map"
            fill
            priority
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-background/95 via-background/80 to-background/70" />
        </div>
        
        <div className="relative z-10 container px-4 py-12 md:py-16 md:px-6">
          <div className="max-w-2xl">
            <Badge variant="outline" className="border-primary/50 text-primary mb-3">COVERAGE AREAS</Badge>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">
              Where We Provide Tire Assistance
            </h1>
            <p className="text-lg text-muted-foreground">
              Fast, reliable roadside tire assistance across Southern California's Inland Empire.
            </p>
          </div>
        </div>
      </section>
      
      {/* Coverage Overview Section */}
      <section className="py-6 md:py-8 container px-4 md:px-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Cities List - Left Side */}
          <div className="lg:col-span-2 space-y-6">
            {/* Complete Coverage Areas */}
            <div>
              <div className="mb-4">
                <h2 className="text-xl md:text-2xl font-bold mb-2">Complete Coverage Areas</h2>
                <p className="text-muted-foreground text-sm">
                  From Pomona to Blythe - all cities and neighboring areas covered 24/7.
                </p>
              </div>

              <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-lg p-4">
                <div className="text-sm text-muted-foreground leading-relaxed space-y-3">
                  <p>
                    <strong className="text-foreground">Inland Empire:</strong> San Bernardino • Riverside • Fontana • Ontario • Rancho Cucamonga • Rialto • Colton • Redlands • Highland • Yucaipa • Beaumont • Banning
                  </p>
                  <p>
                    <strong className="text-foreground">Pomona Valley:</strong> Pomona • Claremont • La Verne • San Dimas • Diamond Bar • Walnut • West Covina • Covina • Glendora • Monrovia
                  </p>
                  <p>
                    <strong className="text-foreground">Riverside South:</strong> Temecula • Murrieta • Lake Elsinore • Moreno Valley • Hemet • San Jacinto • Menifee • Perris • Winchester
                  </p>
                  <p>
                    <strong className="text-foreground">High Desert:</strong> Victorville • Hesperia • Apple Valley • Adelanto • Barstow • Phelan • Oak Hills • Lucerne Valley
                  </p>
                  <p>
                    <strong className="text-foreground">Desert Communities:</strong> Joshua Tree • Twentynine Palms • Yucca Valley • Desert Hot Springs • Pioneertown • Wonder Valley
                  </p>
                  <p>
                    <strong className="text-foreground">Eastern Areas:</strong> Blythe • Needles • Parker Dam • Desert Center • Ludlow • Amboy • Essex • Cadiz
                  </p>
                </div>
              </div>
            </div>

            {/* Primary Service Areas */}
            <div>
              <div className="mb-4">
                <h3 className="text-lg md:text-xl font-bold mb-2">Primary Service Areas</h3>
                <p className="text-muted-foreground text-sm">
                  Fastest response times guaranteed in these zones.
                </p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {[
                  { area: "Inland Empire", time: "15-30 min", cities: "San Bernardino, Riverside, Fontana" },
                  { area: "Pomona Valley", time: "20-35 min", cities: "Pomona, Claremont, La Verne" },
                  { area: "Temecula Valley", time: "25-40 min", cities: "Temecula, Murrieta, Lake Elsinore" },
                  { area: "Moreno Valley", time: "20-35 min", cities: "Moreno Valley, Perris, Hemet" },
                  { area: "High Desert", time: "30-45 min", cities: "Victorville, Hesperia, Apple Valley" },
                  { area: "Desert Communities", time: "35-50 min", cities: "Joshua Tree, 29 Palms, Yucca Valley" },
                ].map((area, index) => (
                  <Card key={index} className="border-border/50 bg-background/50 backdrop-blur-sm">
                    <CardContent className="p-3">
                      <div className="flex items-center gap-1 mb-2">
                        <MapPin className="h-3 w-3 text-primary flex-shrink-0" />
                        <span className="font-bold text-xs">{area.area}</span>
                      </div>
                      <div className="flex items-center gap-1 mb-2">
                        <Clock className="h-3 w-3 text-primary flex-shrink-0" />
                        <span className="text-xs font-medium">{area.time}</span>
                      </div>
                      <p className="text-xs text-muted-foreground leading-tight">{area.cities}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Extended Coverage Areas */}
            <div>
              <div className="mb-4">
                <h3 className="text-lg md:text-xl font-bold mb-2">Extended Coverage Areas</h3>
                <p className="text-muted-foreground text-sm">
                  Longer response times but same quality service.
                </p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {[
                  { area: "San Bernardino Mountains", time: "45-60 min" },
                  { area: "Coachella Valley", time: "45-60 min" },
                  { area: "Eastern Riverside", time: "50-70 min" },
                  { area: "SB County East", time: "50-70 min" },
                  { area: "Colorado River", time: "60-90 min" },
                  { area: "Remote Desert", time: "90-120 min" },
                ].map((area, index) => (
                  <div key={index} className="p-2 rounded bg-muted/50 text-center">
                    <p className="font-medium text-xs mb-1">{area.area}</p>
                    <p className="text-xs text-muted-foreground">{area.time}</p>
                  </div>
                ))}
              </div>

              <div className="mt-4">
                <Button size="sm" className="gap-2" onClick={() => (window.location.href = "tel:+15593619063")}>
                  <Phone className="h-3 w-3" />
                  Call to Confirm Coverage
                </Button>
              </div>
            </div>
          </div>

          {/* Interactive Map - Right Side (Smaller) */}
          <div className="lg:col-span-1">
            <div className="mb-4">
              <h3 className="text-lg font-bold mb-2">Check Your ZIP Code</h3>
              <p className="text-muted-foreground text-xs">
                Enter your ZIP to see response time.
              </p>
            </div>

            <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-lg p-3">
              <ServiceAreasMap />
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-6 md:py-8 bg-muted/20">
        <div className="container px-4 md:px-6 text-center">
          <p className="text-sm text-muted-foreground mb-3">
            Don't see your city? We service ALL neighboring communities in these regions.
          </p>
          <Button size="sm" className="gap-2" onClick={() => (window.location.href = "tel:+15593619063")}>
            <Phone className="h-3 w-3" />
            Call (************* to Confirm
          </Button>
        </div>
      </section>
    </main>
  );
}
