"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MapPin, Phone, Clock } from "lucide-react"
import ServiceAreasMap from "@/components/service-areas-map"

export default function ServiceAreasPage() {
  return (
    <main className="flex-1">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/placeholder.svg?height=1080&width=1920"
            alt="Service area map"
            fill
            priority
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-background/95 via-background/80 to-background/70" />
        </div>
        
        <div className="relative z-10 container px-4 py-16 md:py-24 lg:py-32 md:px-6">
          <div className="max-w-3xl">
            <Badge variant="outline" className="border-primary/50 text-primary mb-4">COVERAGE AREAS</Badge>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6">
              Where We Provide Tire Assistance
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              TireRescuePro provides fast, reliable roadside tire assistance across major metropolitan areas and surrounding regions.
            </p>
          </div>
        </div>
      </section>
      
      {/* Interactive Map Section */}
      <section className="py-12 md:py-16 container px-4 md:px-6">
        <div className="text-center mb-10 space-y-2">
          <Badge variant="outline" className="border-primary/50 text-primary">INTERACTIVE MAP</Badge>
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight">Check Your Coverage</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Use our interactive map to check if your location is within our service area and see estimated response times.
          </p>
        </div>
        
        <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-lg">
          <ServiceAreasMap />
        </div>
      </section>
      
      {/* Primary Service Areas Section */}
      <section className="py-12 md:py-16 bg-muted/30">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10 space-y-2">
            <Badge variant="outline" className="border-primary/50 text-primary">PRIMARY SERVICE AREAS</Badge>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight">Rapid Response Zones</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              In these areas, we guarantee our fastest response times and full service availability.
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                area: "Inland Empire Core",
                responseTime: "15-30 minutes",
                services: ["24/7 Emergency Response", "All Vehicle Types", "Full Service Range"],
                image: "/placeholder.svg?height=150&width=250",
                cities: "San Bernardino, Riverside, Fontana, Ontario, Rancho Cucamonga"
              },
              {
                area: "Pomona Valley",
                responseTime: "20-35 minutes",
                services: ["24/7 Emergency Response", "All Vehicle Types", "Full Service Range"],
                image: "/placeholder.svg?height=150&width=250",
                cities: "Pomona, Claremont, La Verne, San Dimas, Diamond Bar"
              },
              {
                area: "Temecula Valley",
                responseTime: "25-40 minutes",
                services: ["24/7 Emergency Response", "All Vehicle Types", "Full Service Range"],
                image: "/placeholder.svg?height=150&width=250",
                cities: "Temecula, Murrieta, Lake Elsinore, Wildomar, Canyon Lake"
              },
              {
                area: "Moreno Valley Region",
                responseTime: "20-35 minutes",
                services: ["24/7 Emergency Response", "All Vehicle Types", "Full Service Range"],
                image: "/placeholder.svg?height=150&width=250",
                cities: "Moreno Valley, Perris, Hemet, San Jacinto, Menifee"
              },
              {
                area: "High Desert",
                responseTime: "30-45 minutes",
                services: ["24/7 Emergency Response", "All Vehicle Types", "Full Service Range"],
                image: "/placeholder.svg?height=150&width=250",
                cities: "Victorville, Hesperia, Apple Valley, Adelanto, Barstow"
              },
              {
                area: "Desert Communities",
                responseTime: "35-50 minutes",
                services: ["24/7 Emergency Response", "All Vehicle Types", "Full Service Range"],
                image: "/placeholder.svg?height=150&width=250",
                cities: "Joshua Tree, Twentynine Palms, Yucca Valley, Desert Hot Springs"
              },
            ].map((area, index) => (
              <Card key={index} className="overflow-hidden border-border/50 bg-background/50 backdrop-blur-sm">
                <div className="aspect-[5/3] relative">
                  <Image
                    src={area.image || "/placeholder.svg"}
                    alt={`${area.area} area`}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-background to-transparent" />
                  <div className="absolute bottom-2 left-2 flex items-center gap-1">
                    <MapPin className="h-3 w-3 text-primary" />
                    <span className="font-bold text-sm">{area.area}</span>
                  </div>
                </div>
                <CardContent className="p-3">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-3 w-3 text-primary" />
                    <span className="font-medium text-sm">Response: {area.responseTime}</span>
                  </div>
                  {area.cities && (
                    <div className="mb-2">
                      <p className="text-xs text-muted-foreground">Major Cities:</p>
                      <p className="text-xs font-medium">{area.cities}</p>
                    </div>
                  )}
                  <ul className="space-y-1 text-xs text-muted-foreground">
                    {area.services.map((service, i) => (
                      <li key={i} className="flex items-center gap-1">
                        <div className="h-1 w-1 rounded-full bg-primary" />
                        <span>{service}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      
      {/* Extended Coverage Section */}
      <section className="py-12 md:py-16 container px-4 md:px-6">
        <div className="text-center mb-10 space-y-2">
          <Badge variant="outline" className="border-primary/50 text-primary">EXTENDED COVERAGE</Badge>
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight">Additional Service Areas</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            We also provide service in these extended areas with slightly longer response times.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <h3 className="text-2xl font-bold">Extended Service Regions</h3>
            <p className="text-muted-foreground">
              While our primary service areas guarantee the fastest response times, we also provide service to these extended regions. Response times may be longer, but you can still count on our professional service and quality work.
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
              {[
                { area: "San Bernardino Mountains", time: "45-60 min", cities: "Big Bear, Lake Arrowhead, Crestline, Running Springs" },
                { area: "Coachella Valley", time: "45-60 min", cities: "Palm Springs, Cathedral City, Rancho Mirage, Palm Desert" },
                { area: "Eastern Riverside County", time: "50-70 min", cities: "Beaumont, Banning, Cabazon, Whitewater" },
                { area: "San Bernardino County East", time: "50-70 min", cities: "Yucaipa, Redlands, Highland, Loma Linda" },
                { area: "Colorado River Region", time: "60-90 min", cities: "Blythe, Needles, Parker Dam, Big River" },
                { area: "Remote Desert Areas", time: "90-120 min", cities: "Ludlow, Amboy, Essex, Cadiz" },
              ].map((area, index) => (
                <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-muted/50">
                  <MapPin className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                  <div className="flex-1">
                    <p className="font-medium">{area.area}</p>
                    <p className="text-xs text-muted-foreground mb-1">Response: {area.time}</p>
                    {area.cities && (
                      <p className="text-xs text-muted-foreground">{area.cities}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="pt-4">
              <Button className="gap-2" onClick={() => (window.location.href = "tel:+15593619063")}>
                <Phone className="h-4 w-4" />
                Call to Confirm Coverage
              </Button>
            </div>
          </div>
          
          <div className="relative">
            <div className="aspect-square rounded-lg overflow-hidden">
              <Image
                src="/placeholder.svg?height=600&width=600"
                alt="Extended service area map"
                width={600}
                height={600}
                className="object-cover"
              />
            </div>
            <div className="absolute -bottom-6 -right-6 h-32 w-32 rounded-full bg-primary/10 blur-3xl" />
          </div>
        </div>
      </section>

      {/* Complete Cities List Section */}
      <section className="py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10 space-y-2">
            <Badge variant="outline" className="border-primary/50 text-primary">COMPLETE COVERAGE</Badge>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight">All Cities & Towns We Service</h2>
            <p className="text-muted-foreground max-w-3xl mx-auto">
              We provide comprehensive tire assistance across Southern California's Inland Empire, High Desert, and surrounding communities.
              From Pomona to Blythe, we've got you covered 24/7.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Inland Empire Core */}
            <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="font-bold text-lg mb-4 text-primary">Inland Empire Core</h3>
                <div className="grid grid-cols-1 gap-2 text-sm">
                  {[
                    "San Bernardino", "Riverside", "Fontana", "Ontario", "Rancho Cucamonga",
                    "Rialto", "Colton", "Grand Terrace", "Loma Linda", "Redlands",
                    "Highland", "Yucaipa", "Calimesa", "Beaumont", "Banning"
                  ].map((city, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                      <span>{city}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Pomona Valley & West */}
            <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="font-bold text-lg mb-4 text-primary">Pomona Valley & West</h3>
                <div className="grid grid-cols-1 gap-2 text-sm">
                  {[
                    "Pomona", "Claremont", "La Verne", "San Dimas", "Diamond Bar",
                    "Walnut", "West Covina", "Covina", "Baldwin Park", "Azusa",
                    "Glendora", "Monrovia", "Duarte", "Arcadia", "Temple City"
                  ].map((city, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                      <span>{city}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Riverside County South */}
            <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="font-bold text-lg mb-4 text-primary">Riverside County South</h3>
                <div className="grid grid-cols-1 gap-2 text-sm">
                  {[
                    "Temecula", "Murrieta", "Lake Elsinore", "Wildomar", "Canyon Lake",
                    "Menifee", "Sun City", "Perris", "Moreno Valley", "Hemet",
                    "San Jacinto", "Winchester", "French Valley", "Anza", "Aguanga"
                  ].map((city, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                      <span>{city}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-8">
            {/* High Desert */}
            <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="font-bold text-lg mb-4 text-primary">High Desert</h3>
                <div className="grid grid-cols-1 gap-2 text-sm">
                  {[
                    "Victorville", "Hesperia", "Apple Valley", "Adelanto", "Barstow",
                    "Phelan", "Pinon Hills", "Oak Hills", "Spring Valley Lake", "Lucerne Valley",
                    "Johnson Valley", "Landers", "Flamingo Heights", "Yermo", "Daggett"
                  ].map((city, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                      <span>{city}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Desert Communities */}
            <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="font-bold text-lg mb-4 text-primary">Desert Communities</h3>
                <div className="grid grid-cols-1 gap-2 text-sm">
                  {[
                    "Joshua Tree", "Twentynine Palms", "Yucca Valley", "Desert Hot Springs", "Morongo Valley",
                    "Pioneertown", "Landers", "Flamingo Heights", "Wonder Valley", "Amboy",
                    "Ludlow", "Essex", "Cadiz", "Chambless", "Kelso"
                  ].map((city, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                      <span>{city}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Colorado River & Eastern Areas */}
            <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="font-bold text-lg mb-4 text-primary">Colorado River & East</h3>
                <div className="grid grid-cols-1 gap-2 text-sm">
                  {[
                    "Blythe", "Needles", "Parker Dam", "Big River", "Vidal",
                    "Earp", "Rice", "Vidal Junction", "Desert Center", "Chiriaco Summit",
                    "Ripley", "Palo Verde", "Cibola", "Ehrenberg", "Lake Havasu City"
                  ].map((city, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                      <span>{city}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-10">
            <p className="text-muted-foreground mb-4">
              Don't see your city listed? We service ALL neighboring towns and communities in these regions.
            </p>
            <Button size="lg" className="gap-2" onClick={() => (window.location.href = "tel:+15593619063")}>
              <Phone className="h-4 w-4" />
              Call (************* to Confirm Your Area
            </Button>
          </div>
        </div>
      </section>
    </main>
  );
}
