"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Phone, Menu, FootprintsIcon as Tire } from "lucide-react"
import { useMobile } from "@/hooks/use-mobile"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const isMobile = useMobile()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const navItems = [
    { name: "Home", href: "/" },
    { name: "Services", href: "/services" },
    { name: "Coverage Areas", href: "/service-areas" },
    { name: "About Us", href: "/about" },
    { name: "Contact", href: "/contact" },
  ]

  return (
    <header
      className={cn(
        "sticky top-0 z-40 w-full transition-all duration-200",
        isScrolled ? "bg-background/80 backdrop-blur-md border-b" : "bg-transparent",
      )}
    >
      <div className="container flex h-14 md:h-16 items-center justify-between px-4 md:px-6">
        <Link href="/" className="flex items-center gap-2">
          <Tire className="h-8 w-8 text-primary" />
          <span className="font-bold text-xl tracking-tight">
            TireRescue<span className="text-primary">Pro</span>
          </span>
        </Link>

        {isMobile ? (
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[85%] bg-background/95 backdrop-blur-lg">
              <div className="flex flex-col h-full">
                <div className="flex items-center gap-2 py-4 border-b">
                  <Tire className="h-6 w-6 text-primary" />
                  <span className="font-bold text-lg">TireRescuePro</span>
                </div>
                <nav className="flex flex-col gap-1 mt-6 flex-1">
                  {navItems.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className="text-lg font-medium py-3 px-2 rounded-lg transition-colors hover:bg-muted hover:text-primary"
                    >
                      {item.name}
                    </Link>
                  ))}
                </nav>
                <div className="space-y-3 pb-6">
                  <Button
                    className="w-full gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 h-12"
                    onClick={() => (window.location.href = "tel:+15593619063")}
                  >
                    <Phone className="h-5 w-5" />
                    Emergency Call
                  </Button>
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">24/7 Emergency Line</p>
                    <p className="font-bold">(*************</p>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        ) : (
          <nav className="hidden md:flex items-center gap-6">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-sm font-medium transition-colors hover:text-primary"
              >
                {item.name}
              </Link>
            ))}
            <Button className="gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700">
              <Phone className="h-4 w-4" />
              Emergency Call
            </Button>
          </nav>
        )}
      </div>
    </header>
  )
}
