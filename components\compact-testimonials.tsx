"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Star, ChevronLeft, ChevronRight } from "lucide-react"

const testimonials = [
  {
    name: "<PERSON>",
    location: "Downtown",
    rating: 5,
    text: "Arrived in 20 minutes and fixed my flat tire quickly. Professional service!",
    service: "Flat Tire Repair",
  },
  {
    name: "<PERSON>.",
    location: "North Side",
    rating: 5,
    text: "Emergency tire replacement saved my day. Highly recommend!",
    service: "Tire Replacement",
  },
  {
    name: "<PERSON>",
    location: "West Side",
    rating: 5,
    text: "Great motorcycle tire service. They knew exactly what to do.",
    service: "Motorcycle Service",
  },
]

export default function CompactTestimonials() {
  const [activeIndex, setActiveIndex] = useState(0)

  const nextTestimonial = () => {
    setActiveIndex((current) => (current + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setActiveIndex((current) => (current - 1 + testimonials.length) % testimonials.length)
  }

  const currentTestimonial = testimonials[activeIndex]

  return (
    <section className="py-8 md:py-12 gradient-bg-1 texture-overlay">
      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-1/4 w-32 h-32 bg-yellow-400/5 rounded-full blur-2xl float-animation" />
        <div
          className="absolute bottom-20 right-1/4 w-48 h-48 bg-primary/8 rounded-full blur-3xl float-animation"
          style={{ animationDelay: "1s" }}
        />
      </div>

      <div className="relative z-10 container px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-6">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
              Customer Reviews
            </h2>
            <div className="flex justify-center items-center gap-1 mb-4">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="h-5 w-5 text-yellow-400 fill-yellow-400" />
              ))}
              <span className="ml-2 font-bold">4.9/5 from 2,000+ reviews</span>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-center">
            {/* Navigation */}
            <div className="order-2 lg:order-1 flex justify-center lg:justify-start">
              <Button
                variant="outline"
                size="icon"
                onClick={prevTestimonial}
                className="glass-effect border-gradient hover-lift"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </div>

            {/* Enhanced Testimonial */}
            <div className="order-1 lg:order-2">
              <Card className="glass-effect border-gradient glow-secondary hover-lift">
                <CardContent className="p-6">
                  <div className="text-center space-y-4">
                    {/* Stars */}
                    <div className="flex justify-center gap-1">
                      {[...Array(currentTestimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                      ))}
                    </div>

                    {/* Quote with enhanced styling */}
                    <div className="relative">
                      <div className="absolute -top-2 -left-2 text-4xl text-primary/20 font-serif">"</div>
                      <p className="text-lg italic relative z-10 px-4">{currentTestimonial.text}</p>
                      <div className="absolute -bottom-2 -right-2 text-4xl text-primary/20 font-serif">"</div>
                    </div>

                    {/* Author with gradient */}
                    <div className="pt-2 border-t border-gradient">
                      <p className="font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                        {currentTestimonial.name}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {currentTestimonial.location} • {currentTestimonial.service}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Navigation */}
            <div className="order-3 flex justify-center lg:justify-end">
              <Button
                variant="outline"
                size="icon"
                onClick={nextTestimonial}
                className="glass-effect border-gradient hover-lift"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Enhanced dots indicator */}
          <div className="flex justify-center gap-2 mt-6">
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={`h-2 w-2 rounded-full transition-all duration-300 ${
                  index === activeIndex
                    ? "bg-primary w-4 glow-primary"
                    : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
                }`}
                onClick={() => setActiveIndex(index)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
