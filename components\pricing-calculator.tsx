"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Calculator, Check, Info, Phone } from "lucide-react"

export default function PricingCalculator() {
  const [serviceType, setServiceType] = useState("")
  const [vehicleType, setVehicleType] = useState("")
  const [distance, setDistance] = useState("")
  const [urgency, setUrgency] = useState("standard")
  const [price, setPrice] = useState<null | {
    basePrice: number
    distanceFee: number
    urgencyFee: number
    total: number
  }>(null)

  const calculatePrice = () => {
    // Base prices
    const basePrices = {
      "flat-repair": { car: 75, truck: 95, motorcycle: 65 },
      "tire-replacement": { car: 120, truck: 150, motorcycle: 100 },
      "run-flat": { car: 95, truck: 115, motorcycle: 85 },
      "tire-installation": { car: 85, truck: 110, motorcycle: 75 },
      "tire-rotation": { car: 60, truck: 80, motorcycle: 50 },
      "pressure-check": { car: 40, truck: 50, motorcycle: 35 },
    }

    // Calculate fees
    const basePrice =
      basePrices[serviceType as keyof typeof basePrices]?.[vehicleType as keyof (typeof basePrices)["flat-repair"]] || 0
    const distanceFee = Math.round(Number.parseInt(distance) * 1.5)
    const urgencyFee = urgency === "emergency" ? basePrice * 0.25 : 0

    const total = basePrice + distanceFee + urgencyFee

    setPrice({
      basePrice,
      distanceFee,
      urgencyFee,
      total,
    })
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label htmlFor="service-type">Service Type</Label>
          <Select value={serviceType} onValueChange={setServiceType}>
            <SelectTrigger id="service-type" className="h-12">
              <SelectValue placeholder="Select service" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="flat-repair">Flat Tire Repair</SelectItem>
              <SelectItem value="tire-replacement">Emergency Tire Replacement</SelectItem>
              <SelectItem value="run-flat">Run-Flat Tire Service</SelectItem>
              <SelectItem value="tire-installation">Mobile Tire Installation</SelectItem>
              <SelectItem value="tire-rotation">Tire Rotation & Balancing</SelectItem>
              <SelectItem value="pressure-check">Tire Pressure Check</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="vehicle-type">Vehicle Type</Label>
          <Select value={vehicleType} onValueChange={setVehicleType}>
            <SelectTrigger id="vehicle-type" className="h-12">
              <SelectValue placeholder="Select vehicle type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="car">Car/SUV</SelectItem>
              <SelectItem value="truck">Truck/Commercial</SelectItem>
              <SelectItem value="motorcycle">Motorcycle</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="distance">Approximate Distance (miles)</Label>
          <Input
            id="distance"
            type="number"
            placeholder="e.g. 10"
            value={distance}
            onChange={(e) => setDistance(e.target.value)}
            className="h-12"
          />
        </div>

        <div>
          <Label htmlFor="urgency">Service Urgency</Label>
          <Select value={urgency} onValueChange={setUrgency}>
            <SelectTrigger id="urgency" className="h-12">
              <SelectValue placeholder="Select urgency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="standard">Standard (1-2 hours)</SelectItem>
              <SelectItem value="emergency">Emergency (ASAP)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button
          onClick={calculatePrice}
          className="w-full gap-2 h-12"
          disabled={!serviceType || !vehicleType || !distance || !urgency}
        >
          <Calculator className="h-4 w-4" />
          Calculate Price
        </Button>
      </div>

      {price && (
        <div className="rounded-lg border p-4 space-y-3">
          <h3 className="font-bold text-lg">Price Estimate</h3>

          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Base Service Fee:</span>
              <span>${price.basePrice.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Distance Fee:</span>
              <span>${price.distanceFee.toFixed(2)}</span>
            </div>
            {price.urgencyFee > 0 && (
              <div className="flex justify-between">
                <span>Emergency Fee:</span>
                <span>${price.urgencyFee.toFixed(2)}</span>
              </div>
            )}
            <div className="border-t pt-2 mt-2 flex justify-between font-bold">
              <span>Estimated Total:</span>
              <span>${price.total.toFixed(2)}</span>
            </div>
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground bg-muted/50 p-2 rounded">
            <Info className="h-4 w-4 flex-shrink-0" />
            <span>Final price may vary based on actual conditions and specific tire requirements.</span>
          </div>

          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="inclusions">
              <AccordionTrigger className="text-sm">What's included?</AccordionTrigger>
              <AccordionContent>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span>Professional technician service</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span>Travel to your location</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span>Standard labor and equipment</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span>Basic warranty on service</span>
                  </li>
                </ul>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <Button className="w-full gap-2">
            <Phone className="h-4 w-4" />
            Request This Service
          </Button>
        </div>
      )}
    </div>
  )
}
