"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Wrench,
  Clock,
  Car,
  Truck,
  BikeIcon as Motorcycle,
  Phone,
  CheckCircle,
  Zap,
  Shield,
  MapPin,
  Star,
  ArrowRight,
  Timer,
} from "lucide-react"

const services = {
  emergency: [
    {
      title: "Flat Tire Repair",
      description: "Professional roadside patching and repair service",
      price: "From $75",
      time: "15-30 min",
      icon: <Wrench className="h-6 w-6" />,
      features: ["Permanent patches", "Safety inspection", "Pressure check"],
      popular: true,
    },
    {
      title: "Emergency Replacement",
      description: "Immediate tire installation when repair isn't possible",
      price: "From $120",
      time: "20-40 min",
      icon: <Zap className="h-6 w-6" />,
      features: ["Wide tire selection", "Professional installation", "Disposal included"],
      popular: false,
    },
    {
      title: "Run-Flat Service",
      description: "Specialized assistance for run-flat tire systems",
      price: "From $95",
      time: "25-45 min",
      icon: <Shield className="h-6 w-6" />,
      features: ["TPMS reset", "System diagnostics", "Expert handling"],
      popular: false,
    },
  ],
  vehicles: [
    {
      title: "Cars & SUVs",
      description: "Complete passenger vehicle tire services",
      responseTime: "15-30 min",
      coverage: "All metro areas",
      icon: <Car className="h-8 w-8" />,
      features: [
        "Standard & low-profile tires",
        "Run-flat tire service",
        "TPMS system reset",
        "Seasonal tire changes",
        "Emergency roadside repair",
      ],
      serviceTypes: ["Repair", "Replace", "Install", "Rotate"],
    },
    {
      title: "Commercial Trucks",
      description: "Heavy-duty vehicle tire solutions",
      responseTime: "20-45 min",
      coverage: "Priority dispatch",
      icon: <Truck className="h-8 w-8" />,
      features: [
        "Dual wheel service",
        "Fleet maintenance plans",
        "24/7 priority response",
        "Commercial-grade tires",
        "DOT compliance checks",
      ],
      serviceTypes: ["Emergency", "Fleet", "Maintenance", "Inspection"],
    },
    {
      title: "Motorcycles",
      description: "Specialized motorcycle tire expertise",
      responseTime: "20-40 min",
      coverage: "Bike-specific tools",
      icon: <Motorcycle className="h-8 w-8" />,
      features: [
        "Sport & touring bikes",
        "Cruiser specialists",
        "Performance tire installation",
        "Proper torque specs",
        "Tubeless & tube-type service",
      ],
      serviceTypes: ["Repair", "Replace", "Performance", "Touring"],
    },
  ],
}

export default function QuickServiceTabs() {
  const [activeTab, setActiveTab] = useState("emergency")

  return (
    <section className="py-4 md:py-6 bg-muted/30">
      <div className="container px-4">
        <div className="max-w-4xl mx-auto">
          {/* Compact Header */}
          <div className="text-center mb-4">
            <h2 className="text-lg md:text-xl font-bold mb-2">Our Services</h2>
            <p className="text-muted-foreground text-sm">
              Emergency roadside assistance and tire services.
            </p>
          </div>

          {/* Compact Tab Navigation */}
          <div className="flex gap-2 mb-4 max-w-sm mx-auto">
            <Button
              variant={activeTab === "emergency" ? "default" : "outline"}
              onClick={() => setActiveTab("emergency")}
              className="flex-1 gap-1 h-8 text-xs"
              size="sm"
            >
              <Zap className="h-3 w-3" />
              Emergency
            </Button>
            <Button
              variant={activeTab === "vehicles" ? "default" : "outline"}
              onClick={() => setActiveTab("vehicles")}
              className="flex-1 gap-1 h-8 text-xs"
              size="sm"
            >
              <Car className="h-3 w-3" />
              Vehicles
            </Button>
          </div>

          {/* Emergency Services Tab */}
          {activeTab === "emergency" && (
            <div className="space-y-2">
              {services.emergency.map((service, index) => (
                <Card key={index} className="border-border/50 bg-background/50">
                  {service.popular && (
                    <div className="bg-primary/10 border-b px-3 py-1">
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3 text-primary fill-primary" />
                        <span className="text-xs font-medium text-primary">Most Popular</span>
                      </div>
                    </div>
                  )}
                  <CardContent className="p-3">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded bg-primary/10 text-primary flex-shrink-0">
                        {service.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between gap-2 mb-1">
                          <h3 className="font-bold text-sm">{service.title}</h3>
                          <p className="font-bold text-primary text-sm">{service.price}</p>
                        </div>
                        <p className="text-muted-foreground text-xs mb-2">{service.description}</p>
                        <div className="flex gap-2 text-xs">
                          <div className="flex items-center gap-1 bg-muted/50 rounded px-2 py-1">
                            <Timer className="h-3 w-3 text-green-400" />
                            <span>{service.time}</span>
                          </div>
                          <div className="flex items-center gap-1 bg-muted/50 rounded px-2 py-1">
                            <CheckCircle className="h-3 w-3 text-primary" />
                            <span>Guaranteed</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Compact Emergency CTA */}
              <Card className="border-red-500/30 bg-red-500/5">
                <CardContent className="p-3 text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <div className="h-2 w-2 rounded-full bg-red-500 animate-pulse"></div>
                    <span className="font-bold text-red-400 text-xs">EMERGENCY</span>
                  </div>
                  <Button
                    size="sm"
                    className="gap-2 bg-red-500 hover:bg-red-600 text-xs"
                    onClick={() => (window.location.href = "tel:+15593619063")}
                  >
                    <Phone className="h-3 w-3" />
                    Call (*************
                  </Button>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Vehicle Types Tab */}
          {activeTab === "vehicles" && (
            <div className="space-y-2">
              {services.vehicles.map((vehicle, index) => (
                <Card key={index} className="border-border/50 bg-background/50">
                  <CardContent className="p-3">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="p-2 rounded bg-primary/10 text-primary">
                        {vehicle.icon}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-sm mb-1">{vehicle.title}</h3>
                        <p className="text-muted-foreground text-xs">{vehicle.description}</p>
                      </div>
                    </div>

                    <div className="flex gap-2 text-xs mb-2">
                      <div className="flex items-center gap-1 bg-muted/50 rounded px-2 py-1">
                        <Clock className="h-3 w-3 text-green-400" />
                        <span>{vehicle.responseTime}</span>
                      </div>
                      <div className="flex items-center gap-1 bg-muted/50 rounded px-2 py-1">
                        <MapPin className="h-3 w-3 text-primary" />
                        <span>{vehicle.coverage}</span>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-1">
                      {vehicle.serviceTypes.map((type, i) => (
                        <Badge key={i} variant="secondary" className="text-xs">
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
