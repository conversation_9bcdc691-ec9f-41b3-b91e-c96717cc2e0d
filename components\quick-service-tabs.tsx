"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Wrench,
  Clock,
  Car,
  Truck,
  BikeIcon as Motorcycle,
  Phone,
  CheckCircle,
  Zap,
  Shield,
  MapPin,
  Star,
  ArrowRight,
  Timer,
} from "lucide-react"

const services = {
  emergency: [
    {
      title: "Flat Tire Repair",
      description: "Professional roadside patching and repair service",
      price: "From $75",
      time: "15-30 min",
      icon: <Wrench className="h-6 w-6" />,
      features: ["Permanent patches", "Safety inspection", "Pressure check"],
      popular: true,
    },
    {
      title: "Emergency Replacement",
      description: "Immediate tire installation when repair isn't possible",
      price: "From $120",
      time: "20-40 min",
      icon: <Zap className="h-6 w-6" />,
      features: ["Wide tire selection", "Professional installation", "Disposal included"],
      popular: false,
    },
    {
      title: "Run-Flat Service",
      description: "Specialized assistance for run-flat tire systems",
      price: "From $95",
      time: "25-45 min",
      icon: <Shield className="h-6 w-6" />,
      features: ["TPMS reset", "System diagnostics", "Expert handling"],
      popular: false,
    },
  ],
  vehicles: [
    {
      title: "Cars & SUVs",
      description: "Complete passenger vehicle tire services",
      responseTime: "15-30 min",
      coverage: "All metro areas",
      icon: <Car className="h-8 w-8" />,
      features: [
        "Standard & low-profile tires",
        "Run-flat tire service",
        "TPMS system reset",
        "Seasonal tire changes",
        "Emergency roadside repair",
      ],
      serviceTypes: ["Repair", "Replace", "Install", "Rotate"],
    },
    {
      title: "Commercial Trucks",
      description: "Heavy-duty vehicle tire solutions",
      responseTime: "20-45 min",
      coverage: "Priority dispatch",
      icon: <Truck className="h-8 w-8" />,
      features: [
        "Dual wheel service",
        "Fleet maintenance plans",
        "24/7 priority response",
        "Commercial-grade tires",
        "DOT compliance checks",
      ],
      serviceTypes: ["Emergency", "Fleet", "Maintenance", "Inspection"],
    },
    {
      title: "Motorcycles",
      description: "Specialized motorcycle tire expertise",
      responseTime: "20-40 min",
      coverage: "Bike-specific tools",
      icon: <Motorcycle className="h-8 w-8" />,
      features: [
        "Sport & touring bikes",
        "Cruiser specialists",
        "Performance tire installation",
        "Proper torque specs",
        "Tubeless & tube-type service",
      ],
      serviceTypes: ["Repair", "Replace", "Performance", "Touring"],
    },
  ],
}

export default function QuickServiceTabs() {
  return (
    <section className="py-8 md:py-12 bg-gradient-to-b from-background via-muted/20 to-background">
      <div className="container px-4">
        <div className="max-w-6xl mx-auto">
          {/* Modern Header */}
          <div className="text-center mb-10">
            <Badge variant="outline" className="border-primary/50 text-primary mb-3">
              EMERGENCY SERVICES
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Fast. Professional. Guaranteed.</h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Get back on the road quickly with our expert tire services
            </p>
          </div>

          {/* Modern Square Service Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {services.emergency.map((service, index) => (
              <Card
                key={index}
                className={`group relative overflow-hidden border-0 bg-gradient-to-br from-background to-muted/30 hover:from-background hover:to-muted/50 transition-all duration-300 hover:scale-105 hover:shadow-xl ${
                  service.popular ? 'ring-2 ring-primary/20 shadow-lg' : 'shadow-md'
                }`}
              >
                {service.popular && (
                  <div className="absolute top-0 right-0 bg-gradient-to-l from-primary to-primary/80 text-white px-3 py-1 text-xs font-semibold rounded-bl-lg">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-current" />
                      Most Popular
                    </div>
                  </div>
                )}

                <CardContent className="p-6 h-full flex flex-col">
                  {/* Icon Section */}
                  <div className="mb-6 flex justify-center">
                    <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/20 text-primary group-hover:scale-110 transition-transform duration-300">
                      {service.icon}
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="text-center flex-1 flex flex-col">
                    <h3 className="text-xl font-bold mb-2 text-foreground">{service.title}</h3>
                    <p className="text-muted-foreground text-sm mb-4 flex-1">{service.description}</p>

                    {/* Price */}
                    <div className="mb-4">
                      <p className="text-2xl font-bold text-primary">{service.price}</p>
                    </div>

                    {/* Features */}
                    <div className="space-y-2 mb-6">
                      <div className="flex items-center justify-center gap-2 text-sm">
                        <Timer className="h-4 w-4 text-green-500" />
                        <span className="font-medium">{service.time}</span>
                      </div>
                      <div className="flex items-center justify-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-primary" />
                        <span className="font-medium">Guaranteed</span>
                      </div>
                    </div>

                    {/* CTA Button */}
                    <Button
                      className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary text-white font-semibold"
                      onClick={() => (window.location.href = "tel:+15593619063")}
                    >
                      Request Service
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Emergency CTA Section */}
          <Card className="border-0 bg-gradient-to-r from-red-500/10 via-red-500/5 to-red-500/10 shadow-lg">
            <CardContent className="p-6 text-center">
              <div className="flex items-center justify-center gap-3 mb-4">
                <div className="h-3 w-3 rounded-full bg-red-500 animate-pulse"></div>
                <span className="font-bold text-red-500 text-lg tracking-wide">EMERGENCY ASSISTANCE</span>
                <div className="h-3 w-3 rounded-full bg-red-500 animate-pulse"></div>
              </div>
              <p className="text-muted-foreground mb-6 text-lg">Stranded? Need immediate help?</p>
              <Button
                size="lg"
                className="gap-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold px-8 py-3 text-lg"
                onClick={() => (window.location.href = "tel:+15593619063")}
              >
                <Phone className="h-5 w-5" />
                Call Emergency: (*************
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
