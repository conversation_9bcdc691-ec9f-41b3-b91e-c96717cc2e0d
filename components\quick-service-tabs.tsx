"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Wrench,
  Clock,
  Car,
  Truck,
  BikeIcon as Motorcycle,
  Phone,
  CheckCircle,
  Zap,
  Shield,
  MapPin,
  Star,
  ArrowRight,
  Timer,
} from "lucide-react"

const services = {
  emergency: [
    {
      title: "Flat Tire Repair",
      description: "Professional roadside patching and repair service",
      price: "From $75",
      time: "15-30 min",
      icon: <Wrench className="h-6 w-6" />,
      features: ["Permanent patches", "Safety inspection", "Pressure check"],
      popular: true,
    },
    {
      title: "Emergency Replacement",
      description: "Immediate tire installation when repair isn't possible",
      price: "From $120",
      time: "20-40 min",
      icon: <Zap className="h-6 w-6" />,
      features: ["Wide tire selection", "Professional installation", "Disposal included"],
      popular: false,
    },
    {
      title: "Run-Flat Service",
      description: "Specialized assistance for run-flat tire systems",
      price: "From $95",
      time: "25-45 min",
      icon: <Shield className="h-6 w-6" />,
      features: ["TPMS reset", "System diagnostics", "Expert handling"],
      popular: false,
    },
  ],
  vehicles: [
    {
      title: "Cars & SUVs",
      description: "Complete passenger vehicle tire services",
      responseTime: "15-30 min",
      coverage: "All metro areas",
      icon: <Car className="h-8 w-8" />,
      features: [
        "Standard & low-profile tires",
        "Run-flat tire service",
        "TPMS system reset",
        "Seasonal tire changes",
        "Emergency roadside repair",
      ],
      serviceTypes: ["Repair", "Replace", "Install", "Rotate"],
    },
    {
      title: "Commercial Trucks",
      description: "Heavy-duty vehicle tire solutions",
      responseTime: "20-45 min",
      coverage: "Priority dispatch",
      icon: <Truck className="h-8 w-8" />,
      features: [
        "Dual wheel service",
        "Fleet maintenance plans",
        "24/7 priority response",
        "Commercial-grade tires",
        "DOT compliance checks",
      ],
      serviceTypes: ["Emergency", "Fleet", "Maintenance", "Inspection"],
    },
    {
      title: "Motorcycles",
      description: "Specialized motorcycle tire expertise",
      responseTime: "20-40 min",
      coverage: "Bike-specific tools",
      icon: <Motorcycle className="h-8 w-8" />,
      features: [
        "Sport & touring bikes",
        "Cruiser specialists",
        "Performance tire installation",
        "Proper torque specs",
        "Tubeless & tube-type service",
      ],
      serviceTypes: ["Repair", "Replace", "Performance", "Touring"],
    },
  ],
}

export default function QuickServiceTabs() {
  const [activeTab, setActiveTab] = useState("emergency")
  const [selectedService, setSelectedService] = useState<number | null>(null)

  return (
    <section className="py-8 md:py-12 gradient-bg-2 texture-overlay">
      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 right-20 w-32 h-32 bg-primary/5 rounded-full blur-2xl float-animation" />
        <div
          className="absolute bottom-20 left-20 w-48 h-48 bg-secondary/8 rounded-full blur-3xl float-animation"
          style={{ animationDelay: "3s" }}
        />
      </div>

      <div className="relative z-10 container px-4">
        <div className="max-w-6xl mx-auto">
          {/* Enhanced Header */}
          <div className="text-center mb-8 space-y-4">
            <Badge variant="outline" className="border-primary/50 text-primary glass-effect">
              OUR SERVICES
            </Badge>
            <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
              Professional Tire Solutions
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              From emergency roadside assistance to scheduled maintenance, we provide comprehensive tire services for
              all vehicle types.
            </p>
          </div>

          {/* Enhanced Tab Navigation */}
          <div className="flex flex-col sm:flex-row gap-3 mb-8 max-w-md mx-auto">
            <Button
              variant={activeTab === "emergency" ? "default" : "outline"}
              onClick={() => setActiveTab("emergency")}
              className={`flex-1 gap-2 h-12 transition-all duration-300 ${
                activeTab === "emergency"
                  ? "bg-gradient-to-r from-primary to-red-600 glow-primary"
                  : "glass-effect border-gradient hover-lift"
              }`}
            >
              <Zap className="h-4 w-4" />
              <span>Emergency Services</span>
            </Button>
            <Button
              variant={activeTab === "vehicles" ? "default" : "outline"}
              onClick={() => setActiveTab("vehicles")}
              className={`flex-1 gap-2 h-12 transition-all duration-300 ${
                activeTab === "vehicles"
                  ? "bg-gradient-to-r from-primary to-red-600 glow-primary"
                  : "glass-effect border-gradient hover-lift"
              }`}
            >
              <Car className="h-4 w-4" />
              <span>Vehicle Types</span>
            </Button>
          </div>

          {/* Emergency Services Tab */}
          {activeTab === "emergency" && (
            <div className="space-y-6">
              <div className="space-y-4">
                {services.emergency.map((service, index) => (
                  <Card
                    key={index}
                    className={`glass-effect border-gradient transition-all duration-500 hover-lift ${
                      selectedService === index ? "glow-primary" : ""
                    } ${service.popular ? "border-primary/30" : ""}`}
                  >
                    {service.popular && (
                      <div className="bg-gradient-to-r from-primary/20 to-red-500/20 border-b border-primary/20 px-4 py-2">
                        <div className="flex items-center gap-2">
                          <Star className="h-4 w-4 text-primary fill-primary" />
                          <span className="text-sm font-medium text-primary">Most Popular</span>
                        </div>
                      </div>
                    )}

                    <CardContent className="p-4">
                      <div
                        className="cursor-pointer"
                        onClick={() => setSelectedService(selectedService === index ? null : index)}
                      >
                        {/* Enhanced Header */}
                        <div className="flex items-start gap-4 mb-3">
                          <div className="p-3 rounded-xl bg-gradient-to-br from-primary/20 to-primary/10 text-primary flex-shrink-0 glow-accent">
                            {service.icon}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between gap-2 mb-2">
                              <h3 className="font-bold text-lg leading-tight bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                                {service.title}
                              </h3>
                              <div className="text-right flex-shrink-0">
                                <p className="font-bold text-primary text-lg">{service.price}</p>
                              </div>
                            </div>
                            <p className="text-muted-foreground text-sm mb-3">{service.description}</p>

                            {/* Enhanced quick stats */}
                            <div className="flex flex-wrap gap-3 text-sm">
                              <div className="flex items-center gap-1.5 glass-effect rounded-full px-3 py-1">
                                <Timer className="h-3 w-3 text-green-400" />
                                <span className="font-medium">{service.time}</span>
                              </div>
                              <div className="flex items-center gap-1.5 glass-effect rounded-full px-3 py-1">
                                <CheckCircle className="h-3 w-3 text-primary" />
                                <span className="font-medium">Guaranteed</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Enhanced expandable features */}
                        {selectedService === index && (
                          <div className="mt-4 pt-4 border-t border-gradient space-y-4 animate-in slide-in-from-top-2 duration-300">
                            <div>
                              <h4 className="font-medium mb-2 text-sm text-muted-foreground">What's Included:</h4>
                              <ul className="space-y-2">
                                {service.features.map((feature, i) => (
                                  <li key={i} className="flex items-center gap-2 text-sm">
                                    <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0" />
                                    <span>{feature}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>

                            <div className="flex flex-col sm:flex-row gap-2">
                              <Button className="flex-1 gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 glow-primary">
                                <Phone className="h-4 w-4" />
                                Call for {service.title}
                              </Button>
                              <Button variant="outline" className="flex-1 gap-2 glass-effect border-gradient">
                                <MapPin className="h-4 w-4" />
                                Request Service
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Enhanced Emergency CTA */}
              <Card className="border-red-500/30 glass-effect glow-primary">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center gap-2 mb-3">
                    <div className="h-3 w-3 rounded-full bg-red-500 animate-pulse"></div>
                    <span className="font-bold text-red-400">EMERGENCY ASSISTANCE</span>
                  </div>
                  <p className="text-muted-foreground mb-4">Stranded? Need immediate help?</p>
                  <Button
                    size="lg"
                    className="w-full sm:w-auto gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 pulse-glow"
                    onClick={() => (window.location.href = "tel:+18005551234")}
                  >
                    <Phone className="h-5 w-5" />
                    Call Emergency Line: **************
                  </Button>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Enhanced Vehicle Types Tab */}
          {activeTab === "vehicles" && (
            <div className="space-y-6">
              {services.vehicles.map((vehicle, index) => (
                <Card key={index} className="glass-effect border-gradient overflow-hidden hover-lift">
                  <CardContent className="p-0">
                    {/* Enhanced Header with gradient */}
                    <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent p-4 border-b border-gradient">
                      <div className="flex items-center gap-4">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-primary/20 to-primary/10 text-primary glow-accent">
                          {vehicle.icon}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-bold text-xl mb-1 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                            {vehicle.title}
                          </h3>
                          <p className="text-muted-foreground">{vehicle.description}</p>
                        </div>
                      </div>

                      {/* Enhanced quick stats row */}
                      <div className="flex flex-wrap gap-3 mt-4">
                        <div className="flex items-center gap-1.5 glass-effect rounded-full px-3 py-1.5 text-sm">
                          <Clock className="h-3 w-3 text-green-400" />
                          <span className="font-medium">{vehicle.responseTime}</span>
                        </div>
                        <div className="flex items-center gap-1.5 glass-effect rounded-full px-3 py-1.5 text-sm">
                          <MapPin className="h-3 w-3 text-primary" />
                          <span className="font-medium">{vehicle.coverage}</span>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 space-y-4">
                      {/* Service types with enhanced styling */}
                      <div>
                        <h4 className="font-medium mb-3 text-sm text-muted-foreground">Service Types:</h4>
                        <div className="flex flex-wrap gap-2">
                          {vehicle.serviceTypes.map((type, i) => (
                            <Badge key={i} variant="secondary" className="text-xs glass-effect">
                              {type}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Features grid */}
                      <div>
                        <h4 className="font-medium mb-3 text-sm text-muted-foreground">Specialized Services:</h4>
                        <div className="grid grid-cols-1 gap-2">
                          {vehicle.features.map((feature, i) => (
                            <div key={i} className="flex items-center gap-2 text-sm">
                              <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0" />
                              <span>{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Enhanced action buttons */}
                      <div className="flex flex-col sm:flex-row gap-2 pt-2">
                        <Button className="flex-1 gap-2 bg-gradient-to-r from-primary to-red-600 glow-primary">
                          <Phone className="h-4 w-4" />
                          Get {vehicle.title} Service
                        </Button>
                        <Button variant="outline" className="flex-1 gap-2 glass-effect border-gradient">
                          <ArrowRight className="h-4 w-4" />
                          Learn More
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Enhanced vehicle comparison */}
              <Card className="glass-effect border-gradient glow-secondary">
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg mb-4 text-center bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Why Choose Vehicle-Specific Service?
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div className="text-center space-y-2">
                      <div className="h-12 w-12 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center mx-auto glow-accent">
                        <Wrench className="h-6 w-6 text-primary" />
                      </div>
                      <h4 className="font-medium">Right Tools</h4>
                      <p className="text-xs text-muted-foreground">Vehicle-specific equipment for every job</p>
                    </div>
                    <div className="text-center space-y-2">
                      <div className="h-12 w-12 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center mx-auto glow-accent">
                        <Shield className="h-6 w-6 text-primary" />
                      </div>
                      <h4 className="font-medium">Expert Knowledge</h4>
                      <p className="text-xs text-muted-foreground">Trained technicians for each vehicle type</p>
                    </div>
                    <div className="text-center space-y-2">
                      <div className="h-12 w-12 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center mx-auto glow-accent">
                        <Star className="h-6 w-6 text-primary" />
                      </div>
                      <h4 className="font-medium">Quality Service</h4>
                      <p className="text-xs text-muted-foreground">Guaranteed results every time</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>

      {/* Section divider */}
      <div className="section-divider mt-8" />
    </section>
  )
}
