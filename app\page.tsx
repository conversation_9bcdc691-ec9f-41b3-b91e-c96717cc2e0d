"use client"

import CompactHeroSection from "@/components/compact-hero-section"
import QuickServiceTabs from "@/components/quick-service-tabs"
import CompactTestimonials from "@/components/compact-testimonials"
import FloatingEmergencyButton from "@/components/floating-emergency-button"
import ServiceAreasMap from "@/components/service-areas-map"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, CheckCircle, Clock, Shield, MapPin } from "lucide-react"
import Link from "next/link"

export default function Home() {
  return (
    <main className="flex-1">
      <CompactHeroSection />
      <QuickServiceTabs />

      {/* Minimal Coverage Areas */}
      <section className="py-4 border-t border-border/20">
        <div className="container px-4">
          <div className="max-w-5xl mx-auto">
            <div className="text-center mb-6">
              <h2 className="text-xl font-semibold text-foreground/90 mb-2">Service Areas</h2>
              <p className="text-muted-foreground text-sm max-w-md mx-auto">
                Fast response times across Southern California
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3 mb-6">
              {[
                { area: "San Bernardino", time: "15-30" },
                { area: "Riverside", time: "20-35" },
                { area: "Fontana", time: "25-40" },
                { area: "Ontario", time: "20-35" },
                { area: "Pomona", time: "25-40" },
                { area: "Temecula", time: "35-50" },
              ].map((area, index) => (
                <div key={index} className="text-center p-3 rounded-lg border border-border/30 bg-background/50 hover:bg-background/80 transition-colors">
                  <p className="font-medium text-sm text-foreground/90 mb-1">{area.area}</p>
                  <p className="text-xs text-primary font-medium">{area.time} min</p>
                </div>
              ))}
            </div>

            <div className="text-center">
              <p className="text-xs text-muted-foreground mb-3">
                + Victorville, Moreno Valley, and 20+ more areas
              </p>
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-muted/30 border border-border/20">
                <MapPin className="h-4 w-4 text-primary" />
                <span className="text-sm text-muted-foreground">Enter ZIP code to check coverage</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <CompactTestimonials />

      {/* Why Choose Us - Better grid layout */}
      <section className="py-6 md:py-10">
        <div className="container px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-center mb-6">Why Choose TireRescuePro</h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                {
                  icon: <Clock className="h-8 w-8 text-primary" />,
                  title: "Fast Response",
                  description: "30 min average",
                  detail: "GPS-tracked vehicles",
                },
                {
                  icon: <Shield className="h-8 w-8 text-primary" />,
                  title: "15+ Years",
                  description: "Trusted experience",
                  detail: "Certified technicians",
                },
                {
                  icon: <CheckCircle className="h-8 w-8 text-primary" />,
                  title: "Guaranteed",
                  description: "Quality service",
                  detail: "No hidden fees",
                },
                {
                  icon: <MapPin className="h-8 w-8 text-primary" />,
                  title: "Wide Coverage",
                  description: "25+ service areas",
                  detail: "24/7 availability",
                },
              ].map((item, index) => (
                <Card
                  key={index}
                  className="text-center border-border/50 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all group"
                >
                  <CardContent className="p-4">
                    <div className="mb-3 flex justify-center group-hover:scale-110 transition-transform">
                      {item.icon}
                    </div>
                    <h3 className="font-bold mb-1">{item.title}</h3>
                    <p className="text-sm text-muted-foreground mb-1">{item.description}</p>
                    <p className="text-xs text-muted-foreground">{item.detail}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA - Full width approach */}
      <section className="py-8 md:py-12 bg-gradient-to-r from-primary/20 to-primary/10">
        <div className="container px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
              <div className="text-center md:text-left">
                <h2 className="text-2xl md:text-3xl font-bold mb-2">Need Help Right Now?</h2>
                <p className="text-muted-foreground mb-4">Our technicians are standing by 24/7</p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
                  <Button
                    size="lg"
                    className="gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
                    onClick={() => (window.location.href = "tel:+15593619063")}
                  >
                    Call (*************
                  </Button>
                  <Button asChild size="lg" variant="outline" className="gap-2">
                    <Link href="/services">
                      View All Services
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>

              <Card className="border-border/50 bg-background/50 backdrop-blur-lg">
                <CardContent className="p-4">
                  <div className="text-center space-y-2">
                    <p className="font-bold">Average Response Times</p>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-muted/50 rounded p-2">
                        <p className="font-medium">Emergency</p>
                        <p className="text-primary font-bold">15-30 min</p>
                      </div>
                      <div className="bg-muted/50 rounded p-2">
                        <p className="font-medium">Standard</p>
                        <p className="text-primary font-bold">30-60 min</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <FloatingEmergencyButton />
    </main>
  )
}
