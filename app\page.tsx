"use client"

import CompactHeroSection from "@/components/compact-hero-section"
import SimpleServices from "@/components/simple-services"
import QuickRequestForm from "@/components/quick-request-form"
import CompactTestimonials from "@/components/compact-testimonials"
import FloatingEmergencyButton from "@/components/floating-emergency-button"
import ServiceAreasMap from "@/components/service-areas-map"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, CheckCircle, Clock, Shield, MapPin } from "lucide-react"
import Link from "next/link"

export default function Home() {
  return (
    <main className="flex-1">
      <CompactHeroSection />
      <SimpleServices />

      {/* Service Coverage Section - Updated to match About Us style */}
      <section className="py-12 md:py-16 bg-muted/30">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10 space-y-2">
            <Badge variant="outline" className="border-primary/50 text-primary">
              SERVICE COVERAGE
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight">Serving All of Southern California's Inland Empire</h2>
            <p className="text-muted-foreground max-w-3xl mx-auto">
              From Pomona to Blythe, we provide comprehensive 24/7 tire assistance across the entire Inland Empire,
              High Desert, and surrounding communities. Check your ZIP code below to see if we service your area.
            </p>
          </div>

          {/* Interactive Map Section */}
          <div className="mb-10">
            <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <ServiceAreasMap />
              </CardContent>
            </Card>
          </div>

          {/* Regional Coverage Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                region: "Inland Empire Core",
                cities: ["San Bernardino", "Riverside", "Fontana", "Ontario", "Rancho Cucamonga"],
                responseTime: "15-30 min"
              },
              {
                region: "Pomona Valley",
                cities: ["Pomona", "Claremont", "La Verne", "San Dimas", "Diamond Bar"],
                responseTime: "20-35 min"
              },
              {
                region: "Temecula Valley",
                cities: ["Temecula", "Murrieta", "Lake Elsinore", "Moreno Valley", "Hemet"],
                responseTime: "25-40 min"
              },
              {
                region: "High Desert & Beyond",
                cities: ["Victorville", "Joshua Tree", "29 Palms", "Yucaipa", "Blythe"],
                responseTime: "30-60 min"
              }
            ].map((area, index) => (
              <Card key={index} className="border-border/50 bg-background/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg mb-3 text-primary">{area.region}</h3>
                  <div className="space-y-2 mb-4">
                    {area.cities.map((city, cityIndex) => (
                      <div key={cityIndex} className="flex items-center gap-2 text-sm">
                        <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                        <span>{city}</span>
                      </div>
                    ))}
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="h-1.5 w-1.5 rounded-full bg-muted-foreground"></div>
                      <span>+ All neighboring areas</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <Clock className="h-4 w-4 text-primary" />
                    <span>Response: {area.responseTime}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-10">
            <p className="text-muted-foreground mb-4">
              <strong>Complete Coverage:</strong> We service ALL cities, towns, and communities from Pomona to Blythe,
              including San Bernardino, Riverside, Temecula, Ontario, Fontana, Victorville, Moreno Valley, Hemet,
              Yucaipa, Beaumont, Banning, Joshua Tree, Twentynine Palms, and every neighboring area in between.
            </p>
          </div>
        </div>
      </section>

      <CompactTestimonials />

      {/* Why Choose Us - Better grid layout */}
      <section className="py-6 md:py-10">
        <div className="container px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-center mb-6">Why Choose TireRescuePro</h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                {
                  icon: <Clock className="h-8 w-8 text-primary" />,
                  title: "Fast Response",
                  description: "30 min average",
                  detail: "GPS-tracked vehicles",
                },
                {
                  icon: <Shield className="h-8 w-8 text-primary" />,
                  title: "15+ Years",
                  description: "Trusted experience",
                  detail: "Certified technicians",
                },
                {
                  icon: <CheckCircle className="h-8 w-8 text-primary" />,
                  title: "Guaranteed",
                  description: "Quality service",
                  detail: "No hidden fees",
                },
                {
                  icon: <MapPin className="h-8 w-8 text-primary" />,
                  title: "Wide Coverage",
                  description: "25+ service areas",
                  detail: "24/7 availability",
                },
              ].map((item, index) => (
                <Card
                  key={index}
                  className="text-center border-border/50 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all group"
                >
                  <CardContent className="p-4">
                    <div className="mb-3 flex justify-center group-hover:scale-110 transition-transform">
                      {item.icon}
                    </div>
                    <h3 className="font-bold mb-1">{item.title}</h3>
                    <p className="text-sm text-muted-foreground mb-1">{item.description}</p>
                    <p className="text-xs text-muted-foreground">{item.detail}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA - Full width approach */}
      <section className="py-8 md:py-12 bg-gradient-to-r from-primary/20 to-primary/10">
        <div className="container px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
              <div className="text-center md:text-left">
                <h2 className="text-2xl md:text-3xl font-bold mb-2">Need Help Right Now?</h2>
                <p className="text-muted-foreground mb-4">Our technicians are standing by 24/7</p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
                  <Button
                    size="lg"
                    className="gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
                    onClick={() => (window.location.href = "tel:+15593619063")}
                  >
                    Call (*************
                  </Button>
                  <Button asChild size="lg" variant="outline" className="gap-2">
                    <Link href="/services">
                      View All Services
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>

              <Card className="border-border/50 bg-background/50 backdrop-blur-lg">
                <CardContent className="p-4">
                  <div className="text-center space-y-2">
                    <p className="font-bold">Average Response Times</p>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-muted/50 rounded p-2">
                        <p className="font-medium">Emergency</p>
                        <p className="text-primary font-bold">15-30 min</p>
                      </div>
                      <div className="bg-muted/50 rounded p-2">
                        <p className="font-medium">Standard</p>
                        <p className="text-primary font-bold">30-60 min</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <FloatingEmergencyButton />
    </main>
  )
}
