"use client"

import CompactHeroSection from "@/components/compact-hero-section"
import QuickServiceTabs from "@/components/quick-service-tabs"
import QuickRequestForm from "@/components/quick-request-form"
import CompactTestimonials from "@/components/compact-testimonials"
import FloatingEmergencyButton from "@/components/floating-emergency-button"
import ServiceAreasMap from "@/components/service-areas-map"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, CheckCircle, Clock, Shield, MapPin } from "lucide-react"
import Link from "next/link"

export default function Home() {
  return (
    <main className="flex-1">
      <CompactHeroSection />
      <QuickServiceTabs />
      <QuickRequestForm />

      {/* Quick Coverage Check - Better use of space */}
      <section className="py-6 md:py-10">
        <div className="container px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div className="space-y-4">
                <Badge variant="outline" className="border-primary/50 text-primary">
                  COVERAGE AREAS
                </Badge>
                <h2 className="text-2xl md:text-3xl font-bold">We Cover Your Area</h2>
                <p className="text-muted-foreground">
                  Enter your ZIP code to check if we service your location and see estimated response times.
                </p>

                {/* Quick coverage areas */}
                <div className="grid grid-cols-2 gap-3">
                  {[
                    { area: "Downtown", time: "15-30 min" },
                    { area: "North Side", time: "20-35 min" },
                    { area: "South Side", time: "25-40 min" },
                    { area: "Suburbs", time: "35-50 min" },
                  ].map((area, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 rounded-lg bg-muted/50">
                      <MapPin className="h-4 w-4 text-primary flex-shrink-0" />
                      <div>
                        <p className="font-medium text-sm">{area.area}</p>
                        <p className="text-xs text-muted-foreground">{area.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <ServiceAreasMap />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <CompactTestimonials />

      {/* Why Choose Us - Better grid layout */}
      <section className="py-6 md:py-10">
        <div className="container px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-center mb-6">Why Choose TireRescuePro</h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                {
                  icon: <Clock className="h-8 w-8 text-primary" />,
                  title: "Fast Response",
                  description: "30 min average",
                  detail: "GPS-tracked vehicles",
                },
                {
                  icon: <Shield className="h-8 w-8 text-primary" />,
                  title: "15+ Years",
                  description: "Trusted experience",
                  detail: "Certified technicians",
                },
                {
                  icon: <CheckCircle className="h-8 w-8 text-primary" />,
                  title: "Guaranteed",
                  description: "Quality service",
                  detail: "No hidden fees",
                },
                {
                  icon: <MapPin className="h-8 w-8 text-primary" />,
                  title: "Wide Coverage",
                  description: "25+ service areas",
                  detail: "24/7 availability",
                },
              ].map((item, index) => (
                <Card
                  key={index}
                  className="text-center border-border/50 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all group"
                >
                  <CardContent className="p-4">
                    <div className="mb-3 flex justify-center group-hover:scale-110 transition-transform">
                      {item.icon}
                    </div>
                    <h3 className="font-bold mb-1">{item.title}</h3>
                    <p className="text-sm text-muted-foreground mb-1">{item.description}</p>
                    <p className="text-xs text-muted-foreground">{item.detail}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA - Full width approach */}
      <section className="py-8 md:py-12 bg-gradient-to-r from-primary/20 to-primary/10">
        <div className="container px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
              <div className="text-center md:text-left">
                <h2 className="text-2xl md:text-3xl font-bold mb-2">Need Help Right Now?</h2>
                <p className="text-muted-foreground mb-4">Our technicians are standing by 24/7</p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
                  <Button
                    size="lg"
                    className="gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
                    onClick={() => (window.location.href = "tel:+18005551234")}
                  >
                    Call **************
                  </Button>
                  <Button asChild size="lg" variant="outline" className="gap-2">
                    <Link href="/services">
                      View All Services
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>

              <Card className="border-border/50 bg-background/50 backdrop-blur-lg">
                <CardContent className="p-4">
                  <div className="text-center space-y-2">
                    <p className="font-bold">Average Response Times</p>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-muted/50 rounded p-2">
                        <p className="font-medium">Emergency</p>
                        <p className="text-primary font-bold">15-30 min</p>
                      </div>
                      <div className="bg-muted/50 rounded p-2">
                        <p className="font-medium">Standard</p>
                        <p className="text-primary font-bold">30-60 min</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <FloatingEmergencyButton />
    </main>
  )
}
