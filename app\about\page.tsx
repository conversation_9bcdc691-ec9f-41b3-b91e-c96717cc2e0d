import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Clock, Shield, Award, Users, Wrench, Phone, ArrowRight } from "lucide-react"

export default function AboutPage() {
  return (
    <main className="flex-1">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/placeholder.svg?height=1080&width=1920"
            alt="Tire technicians at work"
            fill
            priority
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-background/95 via-background/80 to-background/70" />
        </div>

        <div className="relative z-10 container px-4 py-16 md:py-24 lg:py-32 md:px-6">
          <div className="max-w-3xl">
            <Badge variant="outline" className="border-primary/50 text-primary mb-4">
              ABOUT US
            </Badge>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6">
              Professional Tire Assistance When You Need It Most
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              With over 15 years of experience, TireRescuePro has been providing fast, reliable roadside tire assistance
              to drivers in need.
            </p>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-12 md:py-16 container px-4 md:px-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6">
            <Badge variant="outline" className="border-primary/50 text-primary">
              OUR STORY
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight">
              From a Single Truck to a Fleet of Emergency Responders
            </h2>
            <p className="text-muted-foreground">
              TireRescuePro started in 2008 with a single truck and a simple mission: to provide fast, reliable tire
              assistance to stranded motorists. Our founder, James Wilson, experienced firsthand the stress and danger
              of being stranded with a flat tire on a busy highway.
            </p>
            <p className="text-muted-foreground">
              That experience inspired him to create a service specifically focused on rapid tire assistance. Today,
              we've grown to a fleet of specialized vehicles and trained technicians, but our mission remains the same -
              to get you back on the road safely and quickly.
            </p>

            <div className="pt-4 grid grid-cols-2 gap-6">
              <div className="space-y-2">
                <h3 className="text-3xl font-bold text-primary">15+</h3>
                <p className="text-muted-foreground">Years of Experience</p>
              </div>
              <div className="space-y-2">
                <h3 className="text-3xl font-bold text-primary">50K+</h3>
                <p className="text-muted-foreground">Tires Serviced</p>
              </div>
              <div className="space-y-2">
                <h3 className="text-3xl font-bold text-primary">30</h3>
                <p className="text-muted-foreground">Service Vehicles</p>
              </div>
              <div className="space-y-2">
                <h3 className="text-3xl font-bold text-primary">25</h3>
                <p className="text-muted-foreground">Service Areas</p>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="aspect-square rounded-lg overflow-hidden">
              <Image
                src="/placeholder.svg?height=600&width=600"
                alt="TireRescuePro founder"
                width={600}
                height={600}
                className="object-cover"
              />
            </div>
            <div className="absolute -bottom-6 -right-6 h-32 w-32 rounded-full bg-primary/10 blur-3xl" />
          </div>
        </div>
      </section>

      {/* Our Values Section */}
      <section className="py-12 md:py-16 bg-muted/30">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10 space-y-2">
            <Badge variant="outline" className="border-primary/50 text-primary">
              OUR VALUES
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight">What Drives Us</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our core values guide everything we do, from how we train our technicians to how we respond to your
              emergency.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                title: "Speed & Reliability",
                description:
                  "We understand that time is critical in roadside emergencies. Our average response time is under 30 minutes.",
                icon: <Clock className="h-10 w-10 text-primary" />,
              },
              {
                title: "Safety First",
                description:
                  "Your safety is our top priority. We follow strict protocols to ensure both you and our technicians remain safe.",
                icon: <Shield className="h-10 w-10 text-primary" />,
              },
              {
                title: "Technical Excellence",
                description:
                  "All our technicians are certified and regularly trained on the latest tire technologies and service techniques.",
                icon: <Award className="h-10 w-10 text-primary" />,
              },
              {
                title: "Customer Focus",
                description:
                  "We treat every customer with respect and empathy, understanding the stress of roadside emergencies.",
                icon: <Users className="h-10 w-10 text-primary" />,
              },
              {
                title: "Quality Equipment",
                description:
                  "We invest in the best tools and equipment to ensure efficient and effective service every time.",
                icon: <Wrench className="h-10 w-10 text-primary" />,
              },
              {
                title: "Transparent Pricing",
                description:
                  "No hidden fees or surprises. We provide clear pricing information before beginning any service.",
                icon: <CheckCircle className="h-10 w-10 text-primary" />,
              },
            ].map((value, index) => (
              <Card
                key={index}
                className="group overflow-hidden border-border/50 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all duration-300"
              >
                <CardContent className="p-6 flex flex-col items-center text-center">
                  <div className="mb-4 p-3 rounded-full bg-primary/10 group-hover:bg-primary/20 transition-colors">
                    {value.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{value.title}</h3>
                  <p className="text-muted-foreground">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Our Team Section */}
      <section className="py-12 md:py-16 container px-4 md:px-6">
        <div className="text-center mb-10 space-y-2">
          <Badge variant="outline" className="border-primary/50 text-primary">
            OUR TEAM
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight">Meet Our Experts</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Our team of certified technicians brings years of experience and a commitment to excellence to every service
            call.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {[
            {
              name: "James Wilson",
              role: "Founder & CEO",
              bio: "15+ years in automotive services",
              image: "/placeholder.svg?height=300&width=300",
            },
            {
              name: "Maria Rodriguez",
              role: "Operations Manager",
              bio: "10+ years managing service fleets",
              image: "/placeholder.svg?height=300&width=300",
            },
            {
              name: "David Chen",
              role: "Lead Technician",
              bio: "ASE Certified Master Technician",
              image: "/placeholder.svg?height=300&width=300",
            },
            {
              name: "Sarah Johnson",
              role: "Customer Service Director",
              bio: "Dedicated to exceptional service",
              image: "/placeholder.svg?height=300&width=300",
            },
          ].map((member, index) => (
            <div key={index} className="group">
              <div className="aspect-square rounded-lg overflow-hidden mb-4 relative">
                <Image
                  src={member.image || "/placeholder.svg"}
                  alt={member.name}
                  width={300}
                  height={300}
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-4">
                  <p className="text-sm">{member.bio}</p>
                </div>
              </div>
              <h3 className="font-bold">{member.name}</h3>
              <p className="text-sm text-muted-foreground">{member.role}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Certifications Section */}
      <section className="py-12 md:py-16 bg-muted/30">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10 space-y-2">
            <Badge variant="outline" className="border-primary/50 text-primary">
              CERTIFICATIONS
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight">Industry Recognized</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our technicians and services meet the highest industry standards.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center justify-items-center">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="bg-background/50 backdrop-blur-sm p-6 rounded-lg border border-border/50 w-full max-w-[200px] aspect-square flex items-center justify-center"
              >
                <div className="text-center">
                  <div className="mx-auto h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                    <Award className="h-8 w-8 text-primary" />
                  </div>
                  <p className="font-bold">Certification {i}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Service Coverage Section */}
      <section className="py-12 md:py-16 bg-muted/30">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10 space-y-2">
            <Badge variant="outline" className="border-primary/50 text-primary">
              SERVICE COVERAGE
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight">Serving All of Southern California's Inland Empire</h2>
            <p className="text-muted-foreground max-w-3xl mx-auto">
              From Pomona to Blythe, we provide comprehensive 24/7 tire assistance across the entire Inland Empire,
              High Desert, and surrounding communities. No matter where you are in our coverage area, help is just a phone call away.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                region: "Inland Empire Core",
                cities: ["San Bernardino", "Riverside", "Fontana", "Ontario", "Rancho Cucamonga"],
                responseTime: "15-30 min"
              },
              {
                region: "Pomona Valley",
                cities: ["Pomona", "Claremont", "La Verne", "San Dimas", "Diamond Bar"],
                responseTime: "20-35 min"
              },
              {
                region: "Temecula Valley",
                cities: ["Temecula", "Murrieta", "Lake Elsinore", "Moreno Valley", "Hemet"],
                responseTime: "25-40 min"
              },
              {
                region: "High Desert & Beyond",
                cities: ["Victorville", "Joshua Tree", "29 Palms", "Yucaipa", "Blythe"],
                responseTime: "30-60 min"
              }
            ].map((area, index) => (
              <Card key={index} className="border-border/50 bg-background/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg mb-3 text-primary">{area.region}</h3>
                  <div className="space-y-2 mb-4">
                    {area.cities.map((city, cityIndex) => (
                      <div key={cityIndex} className="flex items-center gap-2 text-sm">
                        <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                        <span>{city}</span>
                      </div>
                    ))}
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="h-1.5 w-1.5 rounded-full bg-muted-foreground"></div>
                      <span>+ All neighboring areas</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <Clock className="h-4 w-4 text-primary" />
                    <span>Response: {area.responseTime}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-10">
            <p className="text-muted-foreground mb-4">
              <strong>Complete Coverage:</strong> We service ALL cities, towns, and communities from Pomona to Blythe,
              including San Bernardino, Riverside, Temecula, Ontario, Fontana, Victorville, Moreno Valley, Hemet,
              Yucaipa, Beaumont, Banning, Joshua Tree, Twentynine Palms, and every neighboring area in between.
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 md:py-16 container px-4 md:px-6">
        <div className="bg-background/50 backdrop-blur-lg border border-border/50 rounded-xl p-8 md:p-12 shadow-lg text-center">
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">Ready to Experience Our Service?</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto mb-8">
            Save our number in your phone today. You never know when you might need emergency tire assistance.
          </p>

          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button
              size="lg"
              className="gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
              onClick={() => (window.location.href = "tel:+15593619063")}
            >
              <Phone className="h-4 w-4" />
              (*************
            </Button>
            <Button asChild size="lg" variant="outline" className="gap-2">
              <Link href="/contact">
                Contact Us
                <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </main>
  )
}
