"use client"

import type React from "react"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  MessageSquare,
  Send,
  CheckCircle,
  Loader2,
  Facebook,
  Twitter,
  Instagram,
  Youtube,
  Car,
  Truck,
  BikeIcon as Motorcycle,
} from "lucide-react"

export default function ContactPage() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
    contactMethod: "email",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    setTimeout(() => {
      setIsSubmitting(false)
      setIsSuccess(true)
      setTimeout(() => {
        setIsSuccess(false)
        setFormData({
          name: "",
          email: "",
          phone: "",
          subject: "",
          message: "",
          contactMethod: "email",
        })
      }, 3000)
    }, 1500)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <main className="flex-1">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/placeholder.svg?height=1080&width=1920"
            alt="Contact us"
            fill
            priority
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-background/95 via-background/80 to-background/70" />
        </div>

        <div className="relative z-10 container px-4 py-16 md:py-24 md:px-6">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="border-primary/50 text-primary mb-4">
              CONTACT US
            </Badge>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6">
              Get In Touch With Our Team
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Need emergency assistance or have questions? We're here to help 24/7 with multiple ways to reach us.
            </p>

            {/* Emergency Contact Banner */}
            <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6 mb-8">
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-red-500 animate-pulse"></div>
                  <span className="font-bold text-red-600">EMERGENCY ASSISTANCE</span>
                </div>
                <Button
                  size="lg"
                  className="gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
                  onClick={() => (window.location.href = "tel:+***********")}
                >
                  <Phone className="h-5 w-5" />
                  Call (*************
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-8 md:py-12">
        <div className="container px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Multiple Ways to Reach Us</h2>
              <p className="text-muted-foreground">Choose the contact method that works best for you</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                {
                  icon: <Phone className="h-8 w-8 text-primary" />,
                  title: "Emergency Line",
                  subtitle: "24/7 Immediate Help",
                  contact: "(*************",
                  action: () => (window.location.href = "tel:+***********"),
                  description: "For roadside emergencies",
                },
                {
                  icon: <MessageSquare className="h-8 w-8 text-primary" />,
                  title: "General Inquiries",
                  subtitle: "Business Hours",
                  contact: "(*************",
                  action: () => (window.location.href = "tel:+***********"),
                  description: "Questions & scheduling",
                },
                {
                  icon: <Mail className="h-8 w-8 text-primary" />,
                  title: "Email Support",
                  subtitle: "Response within 2 hours",
                  contact: "<EMAIL>",
                  action: () => (window.location.href = "mailto:<EMAIL>"),
                  description: "Non-urgent matters",
                },
                {
                  icon: <MapPin className="h-8 w-8 text-primary" />,
                  title: "Visit Our Office",
                  subtitle: "Mon-Fri 8AM-6PM",
                  contact: "123 Roadside Ave",
                  action: () => window.open("https://maps.google.com", "_blank"),
                  description: "Tireville, TX 75001",
                },
              ].map((method, index) => (
                <Card
                  key={index}
                  className="border-border/50 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all group cursor-pointer"
                  onClick={method.action}
                >
                  <CardContent className="p-6 text-center">
                    <div className="mb-4 flex justify-center group-hover:scale-110 transition-transform">
                      {method.icon}
                    </div>
                    <h3 className="font-bold mb-1">{method.title}</h3>
                    <p className="text-sm text-muted-foreground mb-2">{method.subtitle}</p>
                    <p className="font-bold text-primary mb-1">{method.contact}</p>
                    <p className="text-xs text-muted-foreground">{method.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="py-8 md:py-12 bg-muted/30">
        <div className="container px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Contact Form */}
              <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Send className="h-5 w-5" />
                    Send Us a Message
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Fill out the form below and we'll get back to you as soon as possible.
                  </p>
                </CardHeader>
                <CardContent>
                  {isSuccess ? (
                    <div className="text-center py-8 space-y-4">
                      <div className="h-16 w-16 rounded-full bg-green-500/20 flex items-center justify-center mx-auto">
                        <CheckCircle className="h-8 w-8 text-green-500" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold">Message Sent!</h3>
                        <p className="text-muted-foreground">
                          Thank you for contacting us. We'll respond within 2 hours during business hours.
                        </p>
                      </div>
                    </div>
                  ) : (
                    <form onSubmit={handleSubmit} className="space-y-4">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <Input
                            placeholder="Your Name"
                            value={formData.name}
                            onChange={(e) => handleInputChange("name", e.target.value)}
                            className="h-12"
                            required
                          />
                        </div>
                        <div>
                          <Input
                            type="email"
                            placeholder="Email Address"
                            value={formData.email}
                            onChange={(e) => handleInputChange("email", e.target.value)}
                            className="h-12"
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <Input
                            type="tel"
                            placeholder="Phone Number"
                            value={formData.phone}
                            onChange={(e) => handleInputChange("phone", e.target.value)}
                            className="h-12"
                          />
                        </div>
                        <div>
                          <Select
                            value={formData.subject}
                            onValueChange={(value) => handleInputChange("subject", value)}
                            required
                          >
                            <SelectTrigger className="h-12">
                              <SelectValue placeholder="Subject" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="general">General Inquiry</SelectItem>
                              <SelectItem value="service">Service Question</SelectItem>
                              <SelectItem value="billing">Billing Support</SelectItem>
                              <SelectItem value="feedback">Feedback</SelectItem>
                              <SelectItem value="partnership">Partnership</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div>
                        <Textarea
                          placeholder="Your message..."
                          value={formData.message}
                          onChange={(e) => handleInputChange("message", e.target.value)}
                          className="min-h-[120px] resize-none"
                          required
                        />
                      </div>

                      <div>
                        <p className="text-sm text-muted-foreground mb-2">Preferred contact method:</p>
                        <Select
                          value={formData.contactMethod}
                          onValueChange={(value) => handleInputChange("contactMethod", value)}
                        >
                          <SelectTrigger className="h-12">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="email">Email</SelectItem>
                            <SelectItem value="phone">Phone Call</SelectItem>
                            <SelectItem value="text">Text Message</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Button
                        type="submit"
                        className="w-full h-12 bg-gradient-to-r from-primary to-primary/80"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Sending Message...
                          </>
                        ) : (
                          <>
                            <Send className="h-4 w-4 mr-2" />
                            Send Message
                          </>
                        )}
                      </Button>
                    </form>
                  )}
                </CardContent>
              </Card>

              {/* Contact Information */}
              <div className="space-y-6">
                {/* Business Hours */}
                <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      Business Hours
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span>Emergency Service</span>
                      <span className="font-bold text-green-600">24/7</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Office Hours</span>
                      <span className="font-medium">Mon-Fri 8AM-6PM</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Weekend Office</span>
                      <span className="font-medium">Sat 9AM-4PM</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Sunday</span>
                      <span className="text-muted-foreground">Emergency Only</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Service Areas */}
                <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      Service Areas
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-3">
                      {[
                        "San Bernardino",
                        "Riverside",
                        "Fontana",
                        "Ontario",
                        "Pomona",
                        "Temecula",
                        "Victorville",
                        "Moreno Valley",
                        "Hemet",
                        "Yucaipa",
                        "Beaumont",
                        "Banning",
                        "Joshua Tree",
                        "29 Palms",
                        "Blythe",
                        "+ All Neighboring Areas",
                      ].map((area, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>{area}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Vehicle Types */}
                <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle>Vehicle Types We Service</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <Car className="h-5 w-5 text-primary" />
                        <div>
                          <p className="font-medium">Cars & SUVs</p>
                          <p className="text-xs text-muted-foreground">All passenger vehicles</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Truck className="h-5 w-5 text-primary" />
                        <div>
                          <p className="font-medium">Commercial Vehicles</p>
                          <p className="text-xs text-muted-foreground">Trucks, vans, fleets</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Motorcycle className="h-5 w-5 text-primary" />
                        <div>
                          <p className="font-medium">Motorcycles</p>
                          <p className="text-xs text-muted-foreground">All bike types</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-8 md:py-12">
        <div className="container px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Frequently Asked Questions</h2>
              <p className="text-muted-foreground">Quick answers to common questions</p>
            </div>

            <Tabs defaultValue="general" className="w-full">
              <TabsList className="grid w-full grid-cols-3 mb-6">
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="service">Service</TabsTrigger>
                <TabsTrigger value="billing">Billing</TabsTrigger>
              </TabsList>

              <TabsContent value="general" className="space-y-4">
                {[
                  {
                    question: "How quickly can you respond to an emergency?",
                    answer:
                      "Our average response time is 30 minutes or less in our primary service areas. Response times may vary based on location, traffic, and current demand.",
                  },
                  {
                    question: "Do you provide service 24/7?",
                    answer:
                      "Yes, our emergency tire assistance is available 24 hours a day, 7 days a week, including holidays.",
                  },
                  {
                    question: "What areas do you cover?",
                    answer:
                      "We cover the entire metropolitan area including downtown, all major suburbs, and surrounding areas within a 50-mile radius.",
                  },
                ].map((faq, index) => (
                  <Card key={index} className="border-border/50 bg-background/50 backdrop-blur-sm">
                    <CardContent className="p-4">
                      <h3 className="font-bold mb-2">{faq.question}</h3>
                      <p className="text-muted-foreground text-sm">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              <TabsContent value="service" className="space-y-4">
                {[
                  {
                    question: "What types of tire services do you provide?",
                    answer:
                      "We offer flat tire repair, emergency tire replacement, run-flat tire service, mobile tire installation, tire rotation, and pressure checks.",
                  },
                  {
                    question: "Do you carry tires with you?",
                    answer:
                      "Yes, our service vehicles are stocked with common tire sizes. For specialty tires, we can source them quickly from our warehouse.",
                  },
                  {
                    question: "Can you service run-flat tires?",
                    answer:
                      "Absolutely. Our technicians are trained and equipped to handle all tire technologies including run-flat systems.",
                  },
                ].map((faq, index) => (
                  <Card key={index} className="border-border/50 bg-background/50 backdrop-blur-sm">
                    <CardContent className="p-4">
                      <h3 className="font-bold mb-2">{faq.question}</h3>
                      <p className="text-muted-foreground text-sm">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              <TabsContent value="billing" className="space-y-4">
                {[
                  {
                    question: "What payment methods do you accept?",
                    answer:
                      "We accept all major credit cards, debit cards, cash, and mobile payments like Apple Pay and Google Pay.",
                  },
                  {
                    question: "Do you work with insurance companies?",
                    answer:
                      "Yes, we work with most major insurance providers for roadside assistance coverage. We can bill directly or provide receipts for reimbursement.",
                  },
                  {
                    question: "Are there any hidden fees?",
                    answer:
                      "No, we believe in transparent pricing. All costs are explained upfront before any work begins.",
                  },
                ].map((faq, index) => (
                  <Card key={index} className="border-border/50 bg-background/50 backdrop-blur-sm">
                    <CardContent className="p-4">
                      <h3 className="font-bold mb-2">{faq.question}</h3>
                      <p className="text-muted-foreground text-sm">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </section>

      {/* Social Media & Final CTA */}
      <section className="py-8 md:py-12 bg-gradient-to-r from-primary/20 to-primary/10">
        <div className="container px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
              <div className="text-center md:text-left">
                <h2 className="text-2xl md:text-3xl font-bold mb-4">Stay Connected</h2>
                <p className="text-muted-foreground mb-6">
                  Follow us on social media for tire tips, safety advice, and special offers.
                </p>

                <div className="flex justify-center md:justify-start gap-4 mb-6">
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Facebook className="h-5 w-5" />
                  </Button>
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Twitter className="h-5 w-5" />
                  </Button>
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Instagram className="h-5 w-5" />
                  </Button>
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Youtube className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              <Card className="border-border/50 bg-background/50 backdrop-blur-lg text-center">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-2">Need Immediate Help?</h3>
                  <p className="text-muted-foreground mb-4">Don't wait - call our emergency line now</p>
                  <Button
                    size="lg"
                    className="w-full gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
                    onClick={() => (window.location.href = "tel:+***********")}
                  >
                    <Phone className="h-5 w-5" />
                    Call (*************
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
