"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { MapPin, Loader2 } from "lucide-react"

const formSchema = z.object({
  name: z.string().min(2, { message: "Name is required" }),
  phone: z.string().min(10, { message: "Valid phone number is required" }),
  serviceType: z.string().min(1, { message: "Please select a service type" }),
  location: z.string().min(5, { message: "Location is required" }),
  details: z.string().optional(),
})

export default function ServiceRequestForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLocationLoading, setIsLocationLoading] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      phone: "",
      serviceType: "",
      location: "",
      details: "",
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true)

    // Simulate form submission
    setTimeout(() => {
      console.log(values)
      setIsSubmitting(false)
      setIsSuccess(true)

      // Reset form after showing success message
      setTimeout(() => {
        setIsSuccess(false)
        form.reset()
      }, 3000)
    }, 1500)
  }

  const getLocation = () => {
    setIsLocationLoading(true)

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords

          // Simulate reverse geocoding
          setTimeout(() => {
            form.setValue("location", `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`)
            setIsLocationLoading(false)
          }, 1000)
        },
        (error) => {
          console.error("Error getting location:", error)
          setIsLocationLoading(false)
          form.setError("location", {
            type: "manual",
            message: "Could not get your location. Please enter it manually.",
          })
        },
      )
    } else {
      setIsLocationLoading(false)
      form.setError("location", {
        type: "manual",
        message: "Geolocation is not supported by your browser",
      })
    }
  }

  return (
    <div id="service-request">
      {isSuccess ? (
        <div className="text-center py-8 space-y-4">
          <div className="h-16 w-16 rounded-full bg-green-500/20 flex items-center justify-center mx-auto">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
          <h3 className="text-2xl font-bold">Service Request Submitted!</h3>
          <p className="text-muted-foreground">
            We've received your request and will dispatch a technician to your location as soon as possible.
          </p>
        </div>
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" className="h-12" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input placeholder="(*************" className="h-12" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>



            <FormField
              control={form.control}
              name="serviceType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Service Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select service type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="flat-repair">Flat Tire Repair</SelectItem>
                      <SelectItem value="tire-replacement">Emergency Tire Replacement</SelectItem>
                      <SelectItem value="run-flat">Run-Flat Tire Service</SelectItem>
                      <SelectItem value="tire-installation">Mobile Tire Installation</SelectItem>
                      <SelectItem value="commercial">Commercial Vehicle Assistance</SelectItem>
                      <SelectItem value="motorcycle">Motorcycle Tire Service</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Your Location</FormLabel>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <FormControl className="flex-1">
                      <Input placeholder="Current address or coordinates" className="h-12" {...field} />
                    </FormControl>
                    <Button
                      type="button"
                      variant="outline"
                      className="h-12 px-4 sm:w-12"
                      onClick={getLocation}
                      disabled={isLocationLoading}
                    >
                      {isLocationLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <MapPin className="h-4 w-4" />
                      )}
                      <span className="ml-2 sm:hidden">Use GPS</span>
                    </Button>
                  </div>
                  <FormDescription>Click the location icon to use your current GPS location</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="details"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Details</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Vehicle make/model, tire size if known, or any other relevant information"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="w-full gap-2 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary h-12 text-base font-semibold"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  Submitting Request...
                </>
              ) : (
                "Submit Emergency Request"
              )}
            </Button>
          </form>
        </Form>
      )}
    </div>
  )
}
