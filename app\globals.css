@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 346 77% 49.8%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 346 77% 49.8%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Custom gradient backgrounds for sections */
.gradient-bg-1 {
  background: linear-gradient(135deg, hsl(222.2, 84%, 4.9%) 0%, hsl(217.2, 32.6%, 8%) 50%, hsl(222.2, 84%, 4.9%) 100%);
}

.gradient-bg-2 {
  background: linear-gradient(45deg, hsl(222.2, 84%, 4.9%) 0%, hsl(240, 10%, 6%) 50%, hsl(222.2, 84%, 4.9%) 100%);
}

.gradient-bg-3 {
  background: linear-gradient(-45deg, hsl(222.2, 84%, 4.9%) 0%, hsl(217.2, 32.6%, 7%) 50%, hsl(222.2, 84%, 4.9%) 100%);
}

/* Subtle animated gradients */
.animated-gradient {
  background: linear-gradient(
    -45deg,
    hsl(222.2, 84%, 4.9%),
    hsl(217.2, 32.6%, 8%),
    hsl(240, 10%, 6%),
    hsl(222.2, 84%, 4.9%)
  );
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Subtle glow effects */
.glow-primary {
  box-shadow: 0 0 20px hsla(346, 77%, 49.8%, 0.15);
}

.glow-secondary {
  box-shadow: 0 0 30px hsla(217.2, 32.6%, 17.5%, 0.3);
}

.glow-accent {
  box-shadow: 0 0 25px hsla(240, 10%, 15%, 0.4);
}

/* Mesh gradient overlay */
.mesh-gradient {
  background: radial-gradient(circle at 20% 80%, hsla(346, 77%, 49.8%, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, hsla(217.2, 32.6%, 25%, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, hsla(240, 10%, 15%, 0.06) 0%, transparent 50%);
}

/* Subtle texture overlay */
.texture-overlay {
  position: relative;
}

.texture-overlay::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 1px 1px, hsla(210, 40%, 98%, 0.02) 1px, transparent 0);
  background-size: 20px 20px;
  pointer-events: none;
}

/* Glass morphism effect */
.glass-effect {
  background: hsla(222.2, 84%, 4.9%, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid hsla(217.2, 32.6%, 17.5%, 0.3);
}

/* Floating animation */
.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Pulse glow animation */
.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from {
    box-shadow: 0 0 20px hsla(346, 77%, 49.8%, 0.2);
  }
  to {
    box-shadow: 0 0 30px hsla(346, 77%, 49.8%, 0.4);
  }
}

/* Subtle border animations */
.border-gradient {
  position: relative;
  background: linear-gradient(hsl(222.2, 84%, 4.9%), hsl(222.2, 84%, 4.9%)) padding-box,
    linear-gradient(45deg, hsla(346, 77%, 49.8%, 0.3), hsla(217.2, 32.6%, 25%, 0.3)) border-box;
  border: 1px solid transparent;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 40px hsla(222.2, 84%, 4.9%, 0.5);
}

/* Section dividers */
.section-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, hsla(217.2, 32.6%, 25%, 0.5) 50%, transparent 100%);
}

/* Improved scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(222.2, 84%, 4.9%);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, hsla(346, 77%, 49.8%, 0.8), hsla(217.2, 32.6%, 25%, 0.8));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, hsla(346, 77%, 49.8%, 1), hsla(217.2, 32.6%, 25%, 1));
}
