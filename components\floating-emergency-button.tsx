"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Phone } from "lucide-react"
import { cn } from "@/lib/utils"

export default function FloatingEmergencyButton() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const toggleVisibility = () => {
      // Show button after scrolling down 100px
      if (window.pageYOffset > 100) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }

    window.addEventListener("scroll", toggleVisibility)
    return () => window.removeEventListener("scroll", toggleVisibility)
  }, [])

  return (
    <div
      className={cn(
        "fixed bottom-6 right-6 z-50 transition-all duration-300",
        isVisible ? "translate-y-0 opacity-100" : "translate-y-16 opacity-0",
      )}
    >
      <Button
        size="lg"
        className="h-14 w-14 rounded-full shadow-lg bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 hover:scale-110 transition-all duration-200"
        onClick={() => (window.location.href = "tel:+15593619063")}
      >
        <Phone className="h-6 w-6" />
        <span className="sr-only">Emergency Call</span>
      </Button>

      {/* Pulse animation ring */}
      <div className="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-20"></div>
    </div>
  )
}
