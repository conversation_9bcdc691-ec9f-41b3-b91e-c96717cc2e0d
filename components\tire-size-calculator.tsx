"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Search, Car, Truck, BikeIcon as Motorcycle, Info, Phone } from "lucide-react"

export default function TireSizeCalculator() {
  const [activeTab, setActiveTab] = useState("vehicle")
  const [vehicleType, setVehicleType] = useState("")
  const [make, setMake] = useState("")
  const [model, setModel] = useState("")
  const [year, setYear] = useState("")
  const [width, setWidth] = useState("")
  const [aspectRatio, setAspectRatio] = useState("")
  const [rimSize, setRimSize] = useState("")
  const [results, setResults] = useState<null | {
    size: string
    alternatives: string[]
  }>(null)

  const handleVehicleSearch = () => {
    // Simulate API call to get tire size by vehicle
    setTimeout(() => {
      setResults({
        size: "225/65R17",
        alternatives: ["215/70R17", "235/60R17", "245/55R17"],
      })
    }, 500)
  }

  const handleSizeSearch = () => {
    // Simulate API call to get tire size by dimensions
    if (width && aspectRatio && rimSize) {
      const mainSize = `${width}/${aspectRatio}R${rimSize}`
      setResults({
        size: mainSize,
        alternatives: [
          `${Number.parseInt(width) - 10}/${Number.parseInt(aspectRatio) + 5}R${rimSize}`,
          `${Number.parseInt(width) + 10}/${Number.parseInt(aspectRatio) - 5}R${rimSize}`,
        ],
      })
    }
  }

  return (
    <div>
      <Tabs defaultValue="vehicle" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="vehicle">Find by Vehicle</TabsTrigger>
          <TabsTrigger value="size">Find by Size</TabsTrigger>
        </TabsList>

        <TabsContent value="vehicle" className="space-y-4 pt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="vehicle-type">Vehicle Type</Label>
              <Select value={vehicleType} onValueChange={setVehicleType}>
                <SelectTrigger id="vehicle-type" className="h-12">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="car">
                    <div className="flex items-center gap-2">
                      <Car className="h-4 w-4" />
                      <span>Car/SUV</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="truck">
                    <div className="flex items-center gap-2">
                      <Truck className="h-4 w-4" />
                      <span>Truck</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="motorcycle">
                    <div className="flex items-center gap-2">
                      <Motorcycle className="h-4 w-4" />
                      <span>Motorcycle</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="make">Make</Label>
              <Select value={make} onValueChange={setMake}>
                <SelectTrigger id="make" className="h-12">
                  <SelectValue placeholder="Select make" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="toyota">Toyota</SelectItem>
                  <SelectItem value="honda">Honda</SelectItem>
                  <SelectItem value="ford">Ford</SelectItem>
                  <SelectItem value="chevrolet">Chevrolet</SelectItem>
                  <SelectItem value="bmw">BMW</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="sm:col-span-2 lg:col-span-1">
              <Label htmlFor="year">Year</Label>
              <Select value={year} onValueChange={setYear}>
                <SelectTrigger id="year" className="h-12">
                  <SelectValue placeholder="Select year" />
                </SelectTrigger>
                <SelectContent>
                  {[...Array(10)].map((_, i) => {
                    const year = 2024 - i
                    return (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="model">Model</Label>
            <Select value={model} onValueChange={setModel}>
              <SelectTrigger id="model" className="h-12">
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="camry">Camry</SelectItem>
                <SelectItem value="corolla">Corolla</SelectItem>
                <SelectItem value="rav4">RAV4</SelectItem>
                <SelectItem value="highlander">Highlander</SelectItem>
                <SelectItem value="tacoma">Tacoma</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            onClick={handleVehicleSearch}
            className="w-full gap-2 h-12"
            disabled={!vehicleType || !make || !model || !year}
          >
            <Search className="h-4 w-4" />
            Find Tire Size
          </Button>
        </TabsContent>

        <TabsContent value="size" className="space-y-4 pt-4">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="width">Width (mm)</Label>
              <Input
                id="width"
                placeholder="e.g. 225"
                value={width}
                onChange={(e) => setWidth(e.target.value)}
                className="h-12"
              />
            </div>

            <div>
              <Label htmlFor="aspect-ratio">Aspect Ratio</Label>
              <Input
                id="aspect-ratio"
                placeholder="e.g. 65"
                value={aspectRatio}
                onChange={(e) => setAspectRatio(e.target.value)}
                className="h-12"
              />
            </div>

            <div>
              <Label htmlFor="rim-size">Rim Size (inches)</Label>
              <Input
                id="rim-size"
                placeholder="e.g. 17"
                value={rimSize}
                onChange={(e) => setRimSize(e.target.value)}
                className="h-12"
              />
            </div>
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Info className="h-4 w-4" />
            <span>Example: 225/65R17 (Width/Aspect Ratio/Rim Size)</span>
          </div>

          <Button onClick={handleSizeSearch} className="w-full gap-2" disabled={!width || !aspectRatio || !rimSize}>
            <Search className="h-4 w-4" />
            Find Compatible Tires
          </Button>
        </TabsContent>
      </Tabs>

      {results && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Tire Size Results</CardTitle>
            <CardDescription>
              {activeTab === "vehicle" ? "Based on your vehicle selection" : "Based on your tire dimensions"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-1">Recommended Size</h4>
                <div className="text-2xl font-bold">{results.size}</div>
              </div>

              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-1">Alternative Sizes</h4>
                <div className="flex flex-wrap gap-2">
                  {results.alternatives.map((size, index) => (
                    <div key={index} className="bg-muted/50 px-3 py-1 rounded-md text-sm">
                      {size}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full gap-2">
              <Phone className="h-4 w-4" />
              Request Assistance with This Size
            </Button>
          </CardFooter>
        </Card>
      )}
    </div>
  )
}
