import { MapPin } from "lucide-react"

export default function CityResponseTimes() {
  return (
    <div className="grid grid-cols-1 gap-3">
      {[
        { city: "San Bernardino", time: "15-30 min" },
        { city: "Riverside", time: "20-35 min" },
        { city: "Fontana", time: "25-40 min" },
        { city: "Ontario", time: "20-35 min" },
        { city: "Pomona", time: "30-45 min" },
        { city: "Temecula", time: "35-50 min" },
        { city: "Victorville", time: "40-60 min" },
        { city: "Moreno Valley", time: "30-45 min" },
        { city: "Hemet", time: "45-60 min" },
        { city: "Joshua Tree", time: "50-70 min" },
        { city: "29 Palms", time: "60-80 min" },
        { city: "Blythe", time: "90-120 min" },
      ].map((area, index) => (
        <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-muted/30 border border-border/30">
          <MapPin className="h-4 w-4 text-primary flex-shrink-0" />
          <div className="flex-1">
            <p className="font-medium text-sm">{area.city}</p>
            <p className="text-xs text-muted-foreground">Response: {area.time}</p>
          </div>
        </div>
      ))}
    </div>
  )
}
