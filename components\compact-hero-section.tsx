"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Phone, MapPin, Clock, Shield, Star } from "lucide-react"
import Image from "next/image"

export default function CompactHeroSection() {
  const [isLoading, setIsLoading] = useState(false)

  const handleEmergencyClick = () => {
    setIsLoading(true)
    setTimeout(() => {
      window.location.href = "tel:+15593619063"
      setIsLoading(false)
    }, 500)
  }

  return (
    <section className="relative overflow-hidden">
      {/* Background image layer */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/HIT the ROAD with CONFIDENCE.jpg"
          alt="Hit the road with confidence - Professional tire service"
          fill
          priority
          className="object-cover object-center"
        />
        {/* Animated background elements */}
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary/5 rounded-full blur-3xl float-animation" />
        <div
          className="absolute bottom-20 right-10 w-96 h-96 bg-secondary/10 rounded-full blur-3xl float-animation"
          style={{ animationDelay: "2s" }}
        />
        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-accent/5 rounded-full blur-2xl float-animation"
          style={{ animationDelay: "4s" }}
        />
      </div>

      <div className="relative z-10 container px-4 py-8 md:py-16">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            {/* Main content */}
            <div className="lg:col-span-2 text-center lg:text-left space-y-6">
              {/* Status indicator with enhanced glow */}
              <div className="inline-flex items-center gap-2 rounded-full glass-effect px-4 py-2 text-sm text-green-400 glow-primary">
                <span className="relative flex h-2 w-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-green-400"></span>
                </span>
                Available Now - 24/7 Service
              </div>

              {/* Main headline with gradient text */}
              <div className="space-y-3">
                <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight leading-tight">
                  <span className="bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
                    Emergency Tire Help
                  </span>
                  <span className="block bg-gradient-to-r from-primary via-red-400 to-primary bg-clip-text text-transparent">
                    When You Need It
                  </span>
                </h1>
                <p className="text-lg text-muted-foreground">
                  Professional roadside assistance across Southern California's Inland Empire - from Pomona to Blythe
                </p>
              </div>

              {/* Enhanced quick stats */}
              <div className="flex flex-wrap justify-center lg:justify-start items-center gap-4 text-sm">
                <div className="flex items-center gap-2 glass-effect rounded-full px-4 py-2 hover-lift">
                  <Clock className="h-4 w-4 text-primary" />
                  <span className="font-medium">30 min avg</span>
                </div>
                <div className="flex items-center gap-2 glass-effect rounded-full px-4 py-2 hover-lift">
                  <Shield className="h-4 w-4 text-primary" />
                  <span className="font-medium">15+ years</span>
                </div>
                <div className="flex items-center gap-2 glass-effect rounded-full px-4 py-2 hover-lift">
                  <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                  <span className="font-medium">4.9/5 rating</span>
                </div>
              </div>

              {/* Enhanced CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start pt-4">
                <Button
                  size="lg"
                  className="gap-2 bg-gradient-to-r from-red-500 via-red-600 to-red-700 hover:from-red-600 hover:via-red-700 hover:to-red-800 h-12 px-8 pulse-glow hover-lift"
                  onClick={handleEmergencyClick}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Calling...</span>
                    </div>
                  ) : (
                    <>
                      <Phone className="h-5 w-5" />
                      <span>Call Now</span>
                    </>
                  )}
                </Button>
                <Button
                  asChild
                  size="lg"
                  variant="outline"
                  className="gap-2 h-12 px-8 glass-effect border-gradient hover-lift"
                >
                  <Link href="#quick-request">
                    <MapPin className="h-5 w-5" />
                    <span>Get Help</span>
                  </Link>
                </Button>
              </div>
            </div>

            {/* Enhanced emergency contact card */}
            <div className="lg:col-span-1">
              <div className="glass-effect rounded-2xl p-6 text-center glow-secondary hover-lift">
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-primary/5 to-transparent pointer-events-none" />
                <p className="text-sm text-muted-foreground mb-2">24/7 Emergency Hotline</p>
                <p className="text-2xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  (559) 361-9063
                </p>
                <div className="text-xs text-muted-foreground space-y-2">
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full" />
                    <span>Immediate dispatch</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full" />
                    <span>GPS tracking</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full" />
                    <span>No hidden fees</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
