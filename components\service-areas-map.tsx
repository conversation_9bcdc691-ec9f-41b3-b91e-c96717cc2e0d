"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Search, MapPin, Clock, Check, X, Phone, Mail } from "lucide-react"

interface ServiceAreasMapProps {
  compact?: boolean
}

export default function ServiceAreasMap({ compact = false }: ServiceAreasMapProps) {
  const [zipCode, setZipCode] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [searchResult, setSearchResult] = useState<null | {
    available: boolean
    responseTime: string
    distance: string
  }>(null)

  const checkCoverage = () => {
    setIsSearching(true)

    // Simulate API call to check coverage
    setTimeout(() => {
      // For demo purposes, certain zip codes will return different results
      const lastDigit = Number.parseInt(zipCode.slice(-1))

      if (lastDigit >= 0 && lastDigit <= 6) {
        setSearchResult({
          available: true,
          responseTime: lastDigit <= 3 ? "15-30 minutes" : "30-45 minutes",
          distance: `${lastDigit * 2 + 3} miles`,
        })
      } else {
        setSearchResult({
          available: false,
          responseTime: "N/A",
          distance: "Outside service area",
        })
      }

      setIsSearching(false)
    }, 1000)
  }

  return (
    <div className="space-y-4">
      <div className={`${compact ? "aspect-[2/1] max-h-40" : "aspect-[16/10]"} bg-muted rounded-lg overflow-hidden relative`}>
        {/* Placeholder for an actual map */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <p className={`text-muted-foreground ${compact ? "text-sm" : ""}`}>Interactive coverage map</p>
            <p className="text-xs text-muted-foreground">{compact ? "Use ZIP code search below" : "Use ZIP code search below to check availability"}</p>
          </div>
        </div>

        {/* Coverage circles - these would be dynamic in a real implementation */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[60%] h-[60%] rounded-full border-2 border-primary/30 opacity-70"></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[40%] h-[40%] rounded-full border-2 border-primary/50 opacity-70"></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[20%] h-[20%] rounded-full border-2 border-primary/70 opacity-70"></div>

        {/* Service center pin */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
          <div className="relative">
            <MapPin className={`${compact ? "h-6 w-6" : "h-8 w-8"} text-primary`} />
            <div className={`absolute -bottom-1 left-1/2 -translate-x-1/2 ${compact ? "w-1.5 h-1.5" : "w-2 h-2"} bg-primary rounded-full`}></div>
          </div>
        </div>
      </div>

      <div className="space-y-3">
        <div>
          <Label htmlFor="zip-code" className="text-sm">Check Coverage in Your Area</Label>
          <div className="flex gap-2 mt-1">
            <Input
              id="zip-code"
              placeholder="Enter ZIP code"
              value={zipCode}
              onChange={(e) => setZipCode(e.target.value)}
              className="flex-1 h-9"
            />
            <Button onClick={checkCoverage} disabled={zipCode.length < 5 || isSearching} className="gap-2 h-9 px-3" size="sm">
              {isSearching ? (
                <div className="h-3 w-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <Search className="h-3 w-3" />
              )}
              Check
            </Button>
          </div>
        </div>

        {searchResult && (
          <div
            className={`p-3 rounded-lg ${searchResult.available ? "bg-green-500/10 border border-green-500/30" : "bg-red-500/10 border border-red-500/30"}`}
          >
            <div className="flex items-start gap-2">
              <div
                className={`mt-0.5 h-5 w-5 rounded-full flex items-center justify-center ${searchResult.available ? "bg-green-500/20" : "bg-red-500/20"}`}
              >
                {searchResult.available ? (
                  <Check className={`h-3 w-3 ${searchResult.available ? "text-green-500" : "text-red-500"}`} />
                ) : (
                  <X className={`h-3 w-3 ${searchResult.available ? "text-green-500" : "text-red-500"}`} />
                )}
              </div>
              <div className="flex-1">
                <h4 className="font-bold text-sm">
                  {searchResult.available ? "Service Available!" : "Outside Service Area"}
                </h4>
                <p className="text-xs text-muted-foreground mb-2">
                  {searchResult.available
                    ? `Response time: ${searchResult.responseTime}`
                    : "We don't currently service your area."}
                </p>

                {searchResult.available ? (
                  <Button size="sm" className="gap-2" onClick={() => (window.location.href = "tel:+15593619063")}>
                    <Phone className="h-3 w-3" />
                    Call Now
                  </Button>
                ) : (
                  <Button variant="outline" size="sm" className="gap-2">
                    <Mail className="h-3 w-3" />
                    Notify Me
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
