"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Search, MapPin, Clock, Check, X, Phone, Mail } from "lucide-react"

export default function ServiceAreasMap() {
  const [zipCode, setZipCode] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [searchResult, setSearchResult] = useState<null | {
    available: boolean
    responseTime: string
    distance: string
  }>(null)

  const checkCoverage = () => {
    setIsSearching(true)

    // Simulate API call to check coverage
    setTimeout(() => {
      // For demo purposes, certain zip codes will return different results
      const lastDigit = Number.parseInt(zipCode.slice(-1))

      if (lastDigit >= 0 && lastDigit <= 6) {
        setSearchResult({
          available: true,
          responseTime: lastDigit <= 3 ? "15-30 minutes" : "30-45 minutes",
          distance: `${lastDigit * 2 + 3} miles`,
        })
      } else {
        setSearchResult({
          available: false,
          responseTime: "N/A",
          distance: "Outside service area",
        })
      }

      setIsSearching(false)
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div className="aspect-[16/9] bg-muted rounded-lg overflow-hidden relative">
        {/* Placeholder for an actual map */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <p className="text-muted-foreground">Interactive coverage map</p>
            <p className="text-xs text-muted-foreground">Use the ZIP code search below to check availability</p>
          </div>
        </div>

        {/* Coverage circles - these would be dynamic in a real implementation */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[60%] h-[60%] rounded-full border-2 border-primary/30 opacity-70"></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[40%] h-[40%] rounded-full border-2 border-primary/50 opacity-70"></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[20%] h-[20%] rounded-full border-2 border-primary/70 opacity-70"></div>

        {/* Service center pin */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
          <div className="relative">
            <MapPin className="h-8 w-8 text-primary" />
            <div className="absolute -bottom-1 left-1/2 -translate-x-1/2 w-2 h-2 bg-primary rounded-full"></div>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="zip-code">Check Coverage in Your Area</Label>
          <div className="flex gap-2 mt-1.5">
            <Input
              id="zip-code"
              placeholder="Enter ZIP code"
              value={zipCode}
              onChange={(e) => setZipCode(e.target.value)}
              className="flex-1"
            />
            <Button onClick={checkCoverage} disabled={zipCode.length < 5 || isSearching} className="gap-2">
              {isSearching ? (
                <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
              Check
            </Button>
          </div>
        </div>

        {searchResult && (
          <div
            className={`p-4 rounded-lg ${searchResult.available ? "bg-green-500/10 border border-green-500/30" : "bg-red-500/10 border border-red-500/30"}`}
          >
            <div className="flex items-start gap-3">
              <div
                className={`mt-0.5 h-6 w-6 rounded-full flex items-center justify-center ${searchResult.available ? "bg-green-500/20" : "bg-red-500/20"}`}
              >
                {searchResult.available ? (
                  <Check className={`h-4 w-4 ${searchResult.available ? "text-green-500" : "text-red-500"}`} />
                ) : (
                  <X className={`h-4 w-4 ${searchResult.available ? "text-green-500" : "text-red-500"}`} />
                )}
              </div>
              <div>
                <h4 className="font-bold">
                  {searchResult.available ? "Service Available in Your Area!" : "Outside Our Service Area"}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {searchResult.available
                    ? `We can reach your location in approximately ${searchResult.responseTime}.`
                    : "We're sorry, but we don't currently service your area."}
                </p>

                {searchResult.available && (
                  <div className="mt-3 flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-primary" />
                    <span>
                      Estimated response time: <strong>{searchResult.responseTime}</strong>
                    </span>
                  </div>
                )}

                {searchResult.available ? (
                  <Button className="mt-4 gap-2">
                    <Phone className="h-4 w-4" />
                    Request Service Now
                  </Button>
                ) : (
                  <Button variant="outline" className="mt-4 gap-2">
                    <Mail className="h-4 w-4" />
                    Notify Me When Available
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-6">
        {[
          { city: "San Bernardino", time: "15-30 min" },
          { city: "Riverside", time: "20-35 min" },
          { city: "Fontana", time: "25-40 min" },
          { city: "Ontario", time: "20-35 min" },
          { city: "Pomona", time: "30-45 min" },
          { city: "Temecula", time: "35-50 min" },
          { city: "Victorville", time: "40-60 min" },
          { city: "Moreno Valley", time: "30-45 min" },
          { city: "Hemet", time: "45-60 min" },
          { city: "Joshua Tree", time: "50-70 min" },
          { city: "Blythe", time: "90-120 min" },
          { city: "29 Palms", time: "60-80 min" },
        ].map((area, index) => (
          <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
            <MapPin className="h-5 w-5 text-primary" />
            <div>
              <p className="font-medium">{area.city}</p>
              <p className="text-xs text-muted-foreground">Response: {area.time}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
