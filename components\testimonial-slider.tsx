"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Star, ChevronLeft, ChevronRight, Quote } from "lucide-react"

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    location: "Downtown",
    rating: 5,
    text: "I had a flat tire on the highway during rush hour. TireRescuePro arrived within 25 minutes and had me back on the road in no time. Exceptional service!",
    image: "/placeholder.svg?height=80&width=80",
    service: "Flat Tire Repair",
  },
  {
    id: 2,
    name: "<PERSON>",
    location: "North Side",
    rating: 5,
    text: "Their emergency tire replacement service saved my day. The technician was professional, quick, and even helped me understand what caused the issue. Highly recommend!",
    image: "/placeholder.svg?height=80&width=80",
    service: "Emergency Tire Replacement",
  },
  {
    id: 3,
    name: "<PERSON>",
    location: "West Side",
    rating: 4,
    text: "Great service for my motorcycle tire. They arrived with the right equipment and knowledge for my specific bike model. Will definitely use again if needed.",
    image: "/placeholder.svg?height=80&width=80",
    service: "Motorcycle Tire Service",
  },
  {
    id: 4,
    name: "<PERSON>",
    location: "South Side",
    rating: 5,
    text: "I was stranded with my kids in the car when I got a flat. TireRescuePro arrived quickly and the technician was friendly and reassuring. They turned a stressful situation into a positive experience.",
    image: "/placeholder.svg?height=80&width=80",
    service: "Flat Tire Repair",
  },
  {
    id: 5,
    name: "Robert Martinez",
    location: "East Side",
    rating: 5,
    text: "As a commercial driver, I can't afford downtime. Their commercial vehicle assistance was prompt and efficient. They got me back on schedule with minimal delay.",
    image: "/placeholder.svg?height=80&width=80",
    service: "Commercial Vehicle Assistance",
  },
]

export default function TestimonialSlider() {
  const [activeIndex, setActiveIndex] = useState(0)
  const [autoplay, setAutoplay] = useState(true)

  useEffect(() => {
    if (!autoplay) return

    const interval = setInterval(() => {
      setActiveIndex((current) => (current + 1) % testimonials.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [autoplay])

  const handlePrev = () => {
    setAutoplay(false)
    setActiveIndex((current) => (current - 1 + testimonials.length) % testimonials.length)
  }

  const handleNext = () => {
    setAutoplay(false)
    setActiveIndex((current) => (current + 1) % testimonials.length)
  }

  return (
    <div className="relative">
      <div className="overflow-hidden">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(-${activeIndex * 100}%)` }}
        >
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="w-full flex-shrink-0 px-4">
              <Card className="border-border/50 bg-background/50 backdrop-blur-sm">
                <CardContent className="p-6 md:p-8">
                  <div className="flex flex-col md:flex-row gap-6 items-start">
                    <div className="flex-shrink-0">
                      <div className="relative h-16 w-16 rounded-full overflow-hidden">
                        <Image
                          src={testimonial.image || "/placeholder.svg"}
                          alt={testimonial.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center gap-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${i < testimonial.rating ? "text-yellow-500 fill-yellow-500" : "text-muted-foreground"}`}
                          />
                        ))}
                      </div>

                      <div className="relative">
                        <Quote className="absolute -top-2 -left-2 h-6 w-6 text-primary/20" />
                        <p className="text-lg relative z-10">{testimonial.text}</p>
                      </div>

                      <div>
                        <p className="font-bold">{testimonial.name}</p>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <span>{testimonial.location}</span>
                          <span>•</span>
                          <span>{testimonial.service}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-center mt-6 gap-2">
        <Button variant="outline" size="icon" onClick={handlePrev} className="rounded-full">
          <ChevronLeft className="h-4 w-4" />
          <span className="sr-only">Previous testimonial</span>
        </Button>

        <div className="flex items-center gap-2">
          {testimonials.map((_, index) => (
            <button
              key={index}
              className={`h-2 w-2 rounded-full transition-all ${
                index === activeIndex ? "bg-primary w-4" : "bg-muted-foreground/30"
              }`}
              onClick={() => {
                setAutoplay(false)
                setActiveIndex(index)
              }}
            >
              <span className="sr-only">Go to testimonial {index + 1}</span>
            </button>
          ))}
        </div>

        <Button variant="outline" size="icon" onClick={handleNext} className="rounded-full">
          <ChevronRight className="h-4 w-4" />
          <span className="sr-only">Next testimonial</span>
        </Button>
      </div>
    </div>
  )
}
