"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { MapPin, Phone, Loader2, CheckCircle } from "lucide-react"

export default function QuickRequestForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [formData, setFormData] = useState({
    phone: "",
    service: "",
    location: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    setTimeout(() => {
      setIsSubmitting(false)
      setIsSuccess(true)
      setTimeout(() => {
        setIsSuccess(false)
        setFormData({ phone: "", service: "", location: "" })
      }, 3000)
    }, 1500)
  }

  const handleLocationClick = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords
          setFormData((prev) => ({
            ...prev,
            location: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
          }))
        },
        (error) => {
          console.error("Location error:", error)
        },
      )
    }
  }

  return (
    <section id="quick-request" className="py-8 md:py-12 gradient-bg-3 texture-overlay">
      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-10 w-40 h-40 bg-primary/8 rounded-full blur-3xl float-animation" />
        <div
          className="absolute bottom-1/4 right-10 w-56 h-56 bg-secondary/6 rounded-full blur-3xl float-animation"
          style={{ animationDelay: "2s" }}
        />
      </div>

      <div className="relative z-10 container px-4">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
            {/* Enhanced Form */}
            <Card className="glass-effect border-gradient glow-secondary hover-lift">
              <CardHeader className="pb-4">
                <CardTitle className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  Quick Service Request
                </CardTitle>
                <p className="text-sm text-muted-foreground">Get help in 3 simple steps</p>
              </CardHeader>
              <CardContent>
                {isSuccess ? (
                  <div className="text-center py-6 space-y-4">
                    <div className="h-12 w-12 rounded-full bg-green-400/20 flex items-center justify-center mx-auto glow-primary">
                      <CheckCircle className="h-6 w-6 text-green-400" />
                    </div>
                    <div>
                      <h3 className="font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                        Request Submitted!
                      </h3>
                      <p className="text-sm text-muted-foreground">We'll call you within 2 minutes</p>
                    </div>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <Input
                        type="tel"
                        placeholder="Your phone number"
                        value={formData.phone}
                        onChange={(e) => setFormData((prev) => ({ ...prev, phone: e.target.value }))}
                        className="h-12 glass-effect border-gradient"
                        required
                      />
                    </div>

                    <div>
                      <Select
                        value={formData.service}
                        onValueChange={(value) => setFormData((prev) => ({ ...prev, service: value }))}
                        required
                      >
                        <SelectTrigger className="h-12 glass-effect border-gradient">
                          <SelectValue placeholder="What do you need?" />
                        </SelectTrigger>
                        <SelectContent className="glass-effect">
                          <SelectItem value="flat-repair">Flat Tire Repair</SelectItem>
                          <SelectItem value="tire-replacement">Tire Replacement</SelectItem>
                          <SelectItem value="run-flat">Run-Flat Service</SelectItem>
                          <SelectItem value="other">Other/Not Sure</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex gap-2">
                      <Input
                        placeholder="Your location"
                        value={formData.location}
                        onChange={(e) => setFormData((prev) => ({ ...prev, location: e.target.value }))}
                        className="h-12 flex-1 glass-effect border-gradient"
                        required
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        className="h-12 w-12 glass-effect border-gradient hover-lift"
                        onClick={handleLocationClick}
                      >
                        <MapPin className="h-4 w-4" />
                      </Button>
                    </div>

                    <Button
                      type="submit"
                      className="w-full h-12 bg-gradient-to-r from-primary via-red-600 to-primary hover:from-red-600 hover:via-red-700 hover:to-red-600 glow-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Submitting...
                        </>
                      ) : (
                        "Request Service"
                      )}
                    </Button>
                  </form>
                )}
              </CardContent>
            </Card>

            {/* Enhanced Info Panel */}
            <div className="space-y-4">
              <Card className="glass-effect border-gradient hover-lift">
                <CardContent className="p-4">
                  <h3 className="font-bold mb-3 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    What Happens Next?
                  </h3>
                  <div className="space-y-3">
                    <div className="flex gap-3">
                      <div className="h-6 w-6 rounded-full bg-gradient-to-br from-primary/30 to-primary/20 flex items-center justify-center text-xs font-bold text-primary glow-accent">
                        1
                      </div>
                      <div>
                        <p className="font-medium text-sm">We call you back</p>
                        <p className="text-xs text-muted-foreground">Within 2 minutes to confirm details</p>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <div className="h-6 w-6 rounded-full bg-gradient-to-br from-primary/30 to-primary/20 flex items-center justify-center text-xs font-bold text-primary glow-accent">
                        2
                      </div>
                      <div>
                        <p className="font-medium text-sm">Technician dispatched</p>
                        <p className="text-xs text-muted-foreground">GPS tracking sent to your phone</p>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <div className="h-6 w-6 rounded-full bg-gradient-to-br from-primary/30 to-primary/20 flex items-center justify-center text-xs font-bold text-primary glow-accent">
                        3
                      </div>
                      <div>
                        <p className="font-medium text-sm">Problem solved</p>
                        <p className="text-xs text-muted-foreground">Back on the road quickly</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-effect border-gradient hover-lift">
                <CardContent className="p-4 text-center">
                  <p className="text-sm text-muted-foreground mb-3">Need immediate help?</p>
                  <Button
                    variant="outline"
                    className="gap-2 w-full glass-effect border-gradient pulse-glow"
                    onClick={() => (window.location.href = "tel:+15593619063")}
                  >
                    <Phone className="h-4 w-4" />
                    Call (*************
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
