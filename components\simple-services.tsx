"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Wrench, Zap, Shield, Phone, Clock, CheckCircle } from "lucide-react"

const services = [
  {
    title: "Flat Tire Repair",
    description: "Professional roadside patching and repair service",
    price: "From $75",
    time: "15-30 min",
    icon: <Wrench className="h-5 w-5" />,
    popular: true,
  },
  {
    title: "Emergency Replacement",
    description: "Immediate tire installation when repair isn't possible",
    price: "From $120",
    time: "20-40 min",
    icon: <Zap className="h-5 w-5" />,
    popular: false,
  },
  {
    title: "Run-Flat Service",
    description: "Specialized assistance for run-flat tire systems",
    price: "From $95",
    time: "25-45 min",
    icon: <Shield className="h-5 w-5" />,
    popular: false,
  },
]

export default function SimpleServices() {
  return (
    <section className="py-8 md:py-10">
      <div className="container px-4">
        <div className="max-w-4xl mx-auto">
          {/* Simple Header */}
          <div className="text-center mb-6">
            <Badge variant="outline" className="border-primary/50 text-primary mb-2">
              OUR SERVICES
            </Badge>
            <h2 className="text-2xl md:text-3xl font-bold mb-2">Emergency Tire Services</h2>
            <p className="text-muted-foreground">
              Fast, professional roadside assistance when you need it most
            </p>
          </div>

          {/* Compact Services Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {services.map((service, index) => (
              <Card
                key={index}
                className={`border-border/50 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all ${
                  service.popular ? "border-primary/30 ring-1 ring-primary/20" : ""
                }`}
              >
                {service.popular && (
                  <div className="bg-primary/10 border-b border-primary/20 px-3 py-1">
                    <span className="text-xs font-medium text-primary">Most Popular</span>
                  </div>
                )}
                
                <CardContent className="p-4">
                  <div className="flex items-start gap-3 mb-3">
                    <div className="p-2 rounded-lg bg-primary/10 text-primary flex-shrink-0">
                      {service.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-bold text-lg leading-tight mb-1">{service.title}</h3>
                      <p className="text-primary font-bold text-lg">{service.price}</p>
                    </div>
                  </div>
                  
                  <p className="text-muted-foreground text-sm mb-3">{service.description}</p>
                  
                  <div className="flex items-center gap-3 text-sm mb-3">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3 text-green-400" />
                      <span>{service.time}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <CheckCircle className="h-3 w-3 text-primary" />
                      <span>Guaranteed</span>
                    </div>
                  </div>
                  
                  <Button 
                    size="sm" 
                    className="w-full gap-2"
                    onClick={() => (window.location.href = "tel:+15593619063")}
                  >
                    <Phone className="h-4 w-4" />
                    Call Now
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Simple Emergency CTA */}
          <Card className="border-red-500/30 bg-red-500/5">
            <CardContent className="p-4 text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <div className="h-2 w-2 rounded-full bg-red-500 animate-pulse"></div>
                <span className="font-bold text-red-400 text-sm">EMERGENCY ASSISTANCE</span>
              </div>
              <p className="text-muted-foreground text-sm mb-3">Stranded? Need immediate help?</p>
              <Button
                className="gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
                onClick={() => (window.location.href = "tel:+15593619063")}
              >
                <Phone className="h-4 w-4" />
                Call Emergency: (*************
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
