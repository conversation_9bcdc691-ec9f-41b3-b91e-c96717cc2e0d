"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Phone, MapPin, ArrowRight } from "lucide-react"

export default function HeroSection() {
  const [isLoading, setIsLoading] = useState(false)

  const handleEmergencyClick = () => {
    setIsLoading(true)
    // Simulate loading for demo purposes
    setTimeout(() => {
      window.location.href = "tel:+15593619063"
      setIsLoading(false)
    }, 500)
  }

  return (
    <section className="relative overflow-hidden">
      {/* Background with overlay */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/placeholder.svg?height=1080&width=1920"
          alt="Roadside tire assistance"
          fill
          priority
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-background/95 via-background/80 to-background/70" />
      </div>

      <div className="relative z-10 container px-4 py-16 md:py-24 lg:py-32 md:px-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          <div className="space-y-4 md:space-y-6 text-center lg:text-left">
            <div className="inline-flex items-center gap-2 rounded-full bg-primary/10 px-3 py-1 text-sm text-primary">
              <span className="relative flex h-2 w-2">
                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary opacity-75"></span>
                <span className="relative inline-flex rounded-full h-2 w-2 bg-primary"></span>
              </span>
              24/7 Emergency Tire Service
            </div>

            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight leading-tight">
              Roadside Tire Assistance When You Need It Most
            </h1>

            <p className="text-lg md:text-xl text-muted-foreground max-w-lg mx-auto lg:mx-0">
              Professional tire services available 24/7. Fast response times, certified technicians, and comprehensive
              tire solutions.
            </p>

            <div className="flex flex-col sm:flex-row gap-3 pt-2 justify-center lg:justify-start">
              <Button
                size="lg"
                className="gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 transition-all duration-300 h-12 px-6 text-base"
                onClick={handleEmergencyClick}
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Connecting...</span>
                  </div>
                ) : (
                  <>
                    <Phone className="h-5 w-5" />
                    <span>Emergency Call</span>
                  </>
                )}
              </Button>
              <Button
                asChild
                size="lg"
                variant="outline"
                className="gap-2 border-border/50 backdrop-blur-sm bg-background/30 hover:bg-background/50 h-12 px-6 text-base"
              >
                <Link href="#service-request">
                  <MapPin className="h-5 w-5" />
                  <span>Request Service</span>
                </Link>
              </Button>
            </div>

            <div className="flex items-center gap-4 pt-4 justify-center lg:justify-start">
              <div className="flex -space-x-2">
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className="h-8 w-8 rounded-full bg-muted border-2 border-background flex items-center justify-center overflow-hidden"
                  >
                    <span className="text-xs font-medium">{i}</span>
                  </div>
                ))}
              </div>
              <div className="text-sm">
                <p>
                  <span className="font-bold">4.9/5</span> from <span className="font-bold">2,000+</span> reviews
                </p>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="bg-background/50 backdrop-blur-md border border-border/50 rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <div className="h-10 w-10 rounded-full bg-primary/20 flex items-center justify-center">
                  <Phone className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-bold">Emergency Hotline</h3>
                  <p className="text-xl font-bold">(*************</p>
                </div>
              </div>

              <div className="space-y-4 mb-6">
                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                  <div className="h-8 w-8 rounded-full bg-green-500/20 flex items-center justify-center">
                    <span className="text-green-500 text-sm font-bold">30</span>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Average Response Time</p>
                    <p className="text-xs text-muted-foreground">30 minutes or less</p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                  <div className="h-8 w-8 rounded-full bg-primary/20 flex items-center justify-center">
                    <span className="text-primary text-sm font-bold">15</span>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Years of Experience</p>
                    <p className="text-xs text-muted-foreground">Trusted by thousands</p>
                  </div>
                </div>
              </div>

              <Button asChild className="w-full gap-2">
                <Link href="#service-request">
                  Request Service Now
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-primary/20 blur-2xl" />
            <div className="absolute -bottom-8 -left-8 h-32 w-32 rounded-full bg-primary/10 blur-3xl" />
          </div>
        </div>
      </div>
    </section>
  )
}
