"use client"

import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  CheckCircle,
  Clock,
  Shield,
  Car,
  Truck,
  BikeIcon as Motorcycle,
  ArrowRight,
  Phone,
  Wrench,
  Gauge,
} from "lucide-react"

export default function ServicesPage() {
  return (
    <main className="flex-1">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/seervices page.jpg"
            alt="Tire services"
            fill
            priority
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-background/95 via-background/80 to-background/70" />
        </div>

        <div className="relative z-10 container px-4 py-16 md:py-24 lg:py-32 md:px-6">
          <div className="max-w-2xl">
            <Badge variant="outline" className="border-primary/50 text-primary mb-4">
              OUR SERVICES
            </Badge>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6">
              Comprehensive Tire Services for Every Situation
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              From emergency roadside assistance to scheduled maintenance, we provide professional tire services for all
              vehicle types across Southern California's Inland Empire - from Pomona to Blythe.
            </p>
            <Button
              size="lg"
              className="gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
              onClick={() => (window.location.href = "tel:+15593619063")}
            >
              <Phone className="h-4 w-4" />
              Emergency Assistance
            </Button>
          </div>
        </div>
      </section>

      {/* Services Tabs Section */}
      <section className="py-12 md:py-16 container px-4 md:px-6">
        <div className="text-center mb-10 space-y-2">
          <Badge variant="outline" className="border-primary/50 text-primary">
            SERVICES BY VEHICLE
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight">Specialized for Your Vehicle</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            We offer tailored tire services for all types of vehicles, from passenger cars to commercial trucks.
          </p>
        </div>

        <Tabs defaultValue="car" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-8">
            <TabsTrigger value="car" className="gap-2">
              <Car className="h-4 w-4" />
              <span className="hidden sm:inline">Cars & SUVs</span>
              <span className="sm:hidden">Cars</span>
            </TabsTrigger>
            <TabsTrigger value="truck" className="gap-2">
              <Truck className="h-4 w-4" />
              <span className="hidden sm:inline">Commercial Vehicles</span>
              <span className="sm:hidden">Trucks</span>
            </TabsTrigger>
            <TabsTrigger value="motorcycle" className="gap-2">
              <Motorcycle className="h-4 w-4" />
              <span>Motorcycles</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="car" className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
              <div>
                <Image
                  src="/placeholder.svg?height=400&width=600"
                  alt="Car tire service"
                  width={600}
                  height={400}
                  className="rounded-lg object-cover"
                />
              </div>
              <div className="space-y-4">
                <h3 className="text-2xl font-bold">Car & SUV Tire Services</h3>
                <p className="text-muted-foreground">
                  Our technicians are trained to handle all types of passenger vehicles, from compact cars to large
                  SUVs. We carry a wide range of tire sizes and types to ensure we can address your specific needs.
                </p>
                <ul className="space-y-2">
                  {[
                    "Flat tire repair and patching",
                    "Emergency tire replacement",
                    "Run-flat tire services",
                    "Tire rotation and balancing",
                    "Tire pressure monitoring system service",
                    "Seasonal tire changeovers",
                  ].map((item, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>


          </TabsContent>

          <TabsContent value="truck" className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
              <div>
                <Image
                  src="/placeholder.svg?height=400&width=600"
                  alt="Commercial truck tire service"
                  width={600}
                  height={400}
                  className="rounded-lg object-cover"
                />
              </div>
              <div className="space-y-4">
                <h3 className="text-2xl font-bold">Commercial Vehicle Services</h3>
                <p className="text-muted-foreground">
                  We understand that downtime means lost revenue for commercial vehicles. Our specialized equipment and
                  trained technicians can handle heavy-duty tire services for trucks, buses, and fleet vehicles.
                </p>
                <ul className="space-y-2">
                  {[
                    "Heavy-duty tire repair and replacement",
                    "Commercial-grade tire installation",
                    "Fleet service agreements",
                    "Dual wheel service",
                    "TPMS for commercial vehicles",
                    "24/7 emergency response for fleets",
                  ].map((item, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {[
                {
                  title: "Commercial Tire Repair",
                  description: "Roadside repair for commercial vehicle tires with minimal downtime.",
                  icon: <Wrench className="h-8 w-8 text-primary" />,
                  price: "From $95",
                },
                {
                  title: "Heavy-Duty Replacement",
                  description: "Emergency replacement for commercial vehicle tires.",
                  icon: <Truck className="h-8 w-8 text-primary" />,
                  price: "From $150",
                },
              ].map((service, index) => (
                <Card key={index} className="border-border/50 bg-background/50 backdrop-blur-sm">
                  <CardContent className="p-6 pt-6">
                    <div className="mb-4 p-3 rounded-full bg-primary/10 w-fit">{service.icon}</div>
                    <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                    <p className="text-muted-foreground mb-4">{service.description}</p>
                    <p className="font-bold text-lg">{service.price}</p>
                  </CardContent>
                  <CardFooter className="px-6 pb-6 pt-0">
                    <Button className="w-full gap-2" onClick={() => (window.location.href = "tel:+15593619063")}>
                      <Phone className="h-4 w-4" />
                      Request Service
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="motorcycle" className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
              <div>
                <Image
                  src="/placeholder.svg?height=400&width=600"
                  alt="Motorcycle tire service"
                  width={600}
                  height={400}
                  className="rounded-lg object-cover"
                />
              </div>
              <div className="space-y-4">
                <h3 className="text-2xl font-bold">Motorcycle Tire Services</h3>
                <p className="text-muted-foreground">
                  Motorcycle tires require specialized knowledge and equipment. Our technicians are trained to handle
                  all types of motorcycle tires, from sport bikes to cruisers.
                </p>
                <ul className="space-y-2">
                  {[
                    "Motorcycle-specific tire repair",
                    "Performance tire installation",
                    "Specialized equipment for all bike types",
                    "Proper torque specifications",
                    "Tubeless and tube-type tire service",
                    "Custom and specialty motorcycle tires",
                  ].map((item, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                {
                  title: "Motorcycle Tire Repair",
                  description: "Specialized repair for motorcycle tires with proper equipment.",
                  icon: <Motorcycle className="h-10 w-10 text-primary" />,
                  price: "From $65",
                },
                {
                  title: "Performance Tire Installation",
                  description: "Expert installation of high-performance motorcycle tires.",
                  icon: <Wrench className="h-10 w-10 text-primary" />,
                  price: "From $75",
                },
                {
                  title: "Emergency Roadside Assistance",
                  description: "Rapid response for stranded motorcyclists.",
                  icon: <Clock className="h-10 w-10 text-primary" />,
                  price: "From $85",
                },
              ].map((service, index) => (
                <Card key={index} className="border-border/50 bg-background/50 backdrop-blur-sm">
                  <CardContent className="p-6 pt-6">
                    <div className="mb-4 p-3 rounded-full bg-primary/10 w-fit">{service.icon}</div>
                    <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                    <p className="text-muted-foreground mb-4">{service.description}</p>
                    <p className="font-bold text-lg">{service.price}</p>
                  </CardContent>
                  <CardFooter className="px-6 pb-6 pt-0">
                    <Button className="w-full gap-2" onClick={() => (window.location.href = "tel:+15593619063")}>
                      <Phone className="h-4 w-4" />
                      Request Service
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </section>

      {/* Process Section */}
      <section className="py-12 md:py-16 bg-muted/30">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10 space-y-2">
            <Badge variant="outline" className="border-primary/50 text-primary">
              OUR PROCESS
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight">How It Works</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our streamlined process ensures you get the help you need as quickly as possible.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: "1",
                title: "Request Service",
                description:
                  "Call our emergency line or use our online form to request assistance. Provide your location and details about your tire issue.",
              },
              {
                step: "2",
                title: "Quick Dispatch",
                description:
                  "Our dispatcher will immediately assign the nearest technician to your location and provide you with an estimated arrival time.",
              },
              {
                step: "3",
                title: "Professional Service",
                description:
                  "Our certified technician will arrive with the necessary equipment, assess the situation, and perform the required service.",
              },
            ].map((process, index) => (
              <div key={index} className="relative">
                <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-xl p-6 h-full">
                  <div className="absolute -top-5 left-6 h-10 w-10 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-bold">
                    {process.step}
                  </div>
                  <div className="pt-6">
                    <h3 className="text-xl font-bold mb-3">{process.title}</h3>
                    <p className="text-muted-foreground">{process.description}</p>
                  </div>
                </div>
                {index < 2 && (
                  <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-10">
                    <ArrowRight className="h-6 w-6 text-primary" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-12 md:py-16 container px-4 md:px-6">
        <div className="text-center mb-10 space-y-2">
          <Badge variant="outline" className="border-primary/50 text-primary">
            FAQs
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight">Common Questions</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Find answers to frequently asked questions about our tire services.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
          {[
            {
              question: "How quickly can you respond to an emergency?",
              answer:
                "Our average response time is 30 minutes or less in our primary service areas. Response times may vary based on your location, traffic conditions, and current demand.",
            },
            {
              question: "Do you carry different types of tires?",
              answer:
                "Yes, our service vehicles are stocked with a variety of common tire sizes and types. For specialty tires, we may need to source them from our warehouse or local suppliers.",
            },
            {
              question: "What forms of payment do you accept?",
              answer:
                "We accept all major credit cards, debit cards, cash, and mobile payment options like Apple Pay and Google Pay. We also work with many insurance providers for roadside assistance coverage.",
            },
            {
              question: "Do you offer warranties on your services?",
              answer:
                "Yes, all our tire repairs and replacements come with a standard warranty. The specific terms depend on the service provided, and our technician will explain the details before completing the work.",
            },
            {
              question: "Do you sell new and used tires?",
              answer:
                "Yes! We offer a wide selection of both new and quality used tires. All our tires are professionally inspected and come with installation service. We carry various brands and sizes to fit your budget and vehicle needs.",
            },
            {
              question: "Do I need to provide my own spare tire?",
              answer:
                "If you have a spare tire, we can install it. If you don't have a spare or your spare is not usable, we can provide a new tire for installation (additional cost applies).",
            },
          ].map((faq, index) => (
            <div key={index} className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-lg p-6">
              <h3 className="font-bold text-lg mb-2">{faq.question}</h3>
              <p className="text-muted-foreground">{faq.answer}</p>
            </div>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 md:py-16 bg-gradient-to-r from-primary/20 to-primary/10">
        <div className="container px-4 md:px-6">
          <div className="bg-background/50 backdrop-blur-lg border border-border/50 rounded-xl p-8 md:p-12 shadow-lg text-center">
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">Need Tire Assistance?</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto mb-8">
              Our professional technicians are ready to help you 24/7. Call us now or request service online.
            </p>

            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                size="lg"
                className="gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
                onClick={() => (window.location.href = "tel:+15593619063")}
              >
                <Phone className="h-4 w-4" />
                (*************
              </Button>
              <Button asChild size="lg" variant="outline" className="gap-2">
                <Link href="/#service-request">
                  Request Service Online
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
